package top.resty;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.math.BigInteger;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

public class Main {

    // 标题信息类
    static class HeadingInfo {
        private String text;
        private String styleId;
        private int level;
        private XWPFParagraph paragraph;
        private String bookmarkName;
        private int pageNumber;

        public HeadingInfo(String text, String styleId, int level, XWPFParagraph paragraph) {
            this.text = text;
            this.styleId = styleId;
            this.level = level;
            this.paragraph = paragraph;
            this.bookmarkName = generateSafeBookmarkName(text);
            this.pageNumber = 1; // 默认页码
        }

        /**
         * 生成安全的书签名称
         */
        private static String generateSafeBookmarkName(String text) {
            // 移除特殊字符，只保留字母、数字和下划线
            String safeName = text.replaceAll("[^\\w\\u4e00-\\u9fa5]", "_");
            // 确保以字母开头
            if (!safeName.matches("^[a-zA-Z\\u4e00-\\u9fa5].*")) {
                safeName = "heading_" + safeName;
            }
            // 限制长度并添加唯一标识
            if (safeName.length() > 30) {
                safeName = safeName.substring(0, 30);
            }
            safeName += "_" + System.currentTimeMillis() % 10000;
            return safeName;
        }

        // Getters and Setters
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public String getStyleId() { return styleId; }
        public void setStyleId(String styleId) { this.styleId = styleId; }
        public int getLevel() { return level; }
        public void setLevel(int level) { this.level = level; }
        public XWPFParagraph getParagraph() { return paragraph; }
        public void setParagraph(XWPFParagraph paragraph) { this.paragraph = paragraph; }
        public String getBookmarkName() { return bookmarkName; }
        public void setBookmarkName(String bookmarkName) { this.bookmarkName = bookmarkName; }
        public int getPageNumber() { return pageNumber; }
        public void setPageNumber(int pageNumber) { this.pageNumber = pageNumber; }
    }

    public static void main(String[] args) {
        try {
            String originalDocPath = "测试1.docx";
            String newDocPath = "generated_document_with_toc.docx";

            // 1. 获取原文档内容，如果不存在则创建示例文档
            XWPFDocument originalDocument = loadDocument(originalDocPath);

            // 2. 遍历出原文档中所有的标题并存进集合
            List<HeadingInfo> headings = extractHeadings(originalDocument);

            // 3. 创建新文档，复制原文档内容
            XWPFDocument newDocument = createDocumentCopy(originalDocument);
            originalDocument.close(); // 关闭原文档，不再修改

            // 4. 在新文档中为标题添加书签
            List<HeadingInfo> newHeadings = extractHeadings(newDocument);
            addBookmarksToHeadings(newHeadings);

            // 5. 先用临时页码插入目录
            calculateTemporaryPageNumbers(newHeadings);

            // 6. 在新文档最初的位置插入目录
            insertTableOfContents(newDocument, newHeadings);

            // 7. 计算目录实际占用的页数，重新计算页码
            int tocPages = calculateTocPages(newDocument, newHeadings.size());
            recalculatePageNumbers(newHeadings, tocPages);

            // 8. 更新目录中的页码
            updateTocPageNumbers(newDocument, newHeadings);

            // 9. 保存新文档
            saveDocument(newDocument, newDocPath);

            System.out.println("新文档生成成功: " + newDocPath);
            System.out.println("原文档保持不变: " + originalDocPath);
            System.out.println("找到 " + newHeadings.size() + " 个标题");
            System.out.println("目录占用 " + tocPages + " 页");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 1. 加载文档
     */
    private static XWPFDocument loadDocument(String docPath) throws Exception {
        try (FileInputStream fis = new FileInputStream(docPath)) {
            return new XWPFDocument(fis);
        }
    }

    /**
     * 创建文档副本
     */
    private static XWPFDocument createDocumentCopy(XWPFDocument originalDocument) throws Exception {
        XWPFDocument newDocument = new XWPFDocument();

        // 复制所有段落
        for (XWPFParagraph originalParagraph : originalDocument.getParagraphs()) {
            XWPFParagraph newParagraph = newDocument.createParagraph();
            copyParagraph(originalParagraph, newParagraph);
        }

        System.out.println("文档副本创建完成，共复制 " + originalDocument.getParagraphs().size() + " 个段落");
        return newDocument;
    }

    /**
     * 复制段落内容
     */
    private static void copyParagraph(XWPFParagraph source, XWPFParagraph target) {
        // 复制段落属性
        target.setAlignment(source.getAlignment());
        target.setIndentationLeft(source.getIndentationLeft());
        target.setIndentationRight(source.getIndentationRight());
        target.setSpacingAfter(source.getSpacingAfter());
        target.setSpacingBefore(source.getSpacingBefore());

        // 复制样式
        if (source.getStyleID() != null) {
            target.setStyle(source.getStyleID());
        }

        // 复制所有运行
        for (XWPFRun sourceRun : source.getRuns()) {
            XWPFRun targetRun = target.createRun();
            copyRun(sourceRun, targetRun);
        }
    }

    /**
     * 复制运行内容
     */
    private static void copyRun(XWPFRun source, XWPFRun target) {
        // 复制文本
        String text = source.getText(0);
        if (text != null) {
            target.setText(text);
        }

        // 复制格式
        target.setBold(source.isBold());
        target.setItalic(source.isItalic());
        target.setUnderline(source.getUnderline());

        if (source.getFontFamily() != null) {
            target.setFontFamily(source.getFontFamily());
        }

        if (source.getFontSize() != -1) {
            target.setFontSize(source.getFontSize());
        }

        if (source.getColor() != null) {
            target.setColor(source.getColor());
        }

        // 复制分页符
        if (source.getCTR().getBrList().size() > 0) {
            for (int i = 0; i < source.getCTR().getBrList().size(); i++) {
                target.addBreak(BreakType.PAGE);
            }
        }
    }

    /**
     * 2. 遍历文档中所有的标题并存进集合（使用递归层级分析）
     */
    private static List<HeadingInfo> extractHeadings(XWPFDocument document) {
        List<XWPFParagraph> allParagraphs = document.getParagraphs();
        List<HeadingInfo> headings = new ArrayList<>();

        System.out.println("开始递归分析标题层级关系，共 " + allParagraphs.size() + " 个段落");

        // 递归分析段落层级关系
        analyzeHeadingHierarchy(allParagraphs, headings, 0, new ArrayList<>());

        System.out.println("递归分析完成，共找到 " + headings.size() + " 个有效标题");
        return headings;
    }

    /**
     * 逆向递归分析标题层级关系 - 从子级向前推
     */
    private static void analyzeHeadingHierarchy(List<XWPFParagraph> paragraphs,
                                               List<HeadingInfo> headings,
                                               int startIndex,
                                               List<Integer> currentHierarchy) {

        // 第一步：收集所有标题候选
        List<HeadingCandidate> candidates = new ArrayList<>();

        for (int i = startIndex; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            String styleId = paragraph.getStyleID();

            if (text == null || text.trim().isEmpty()) {
                continue;
            }

            text = text.trim();

            // 分析段落的潜在标题级别
            HeadingCandidate candidate = analyzeHeadingCandidate(text, styleId, paragraph);
            candidate.paragraphIndex = i;

            if (candidate.isValidHeading) {
                candidates.add(candidate);
            }
        }

        // 第二步：逆向分析层级关系
        analyzeHierarchyReverse(candidates, paragraphs, headings);
    }

    /**
     * 逆向分析层级关系 - 从子级向前推
     */
    private static void analyzeHierarchyReverse(List<HeadingCandidate> candidates,
                                               List<XWPFParagraph> paragraphs,
                                               List<HeadingInfo> headings) {
        if (candidates.isEmpty()) {
            return;
        }

        // 从最后一个标题开始，向前推导
        for (int i = candidates.size() - 1; i >= 0; i--) {
            HeadingCandidate current = candidates.get(i);

            // 根据后续内容和标题推导当前标题级别
            int adjustedLevel = determineHeadingLevelReverse(current, candidates, i, paragraphs);

            // 创建层级
            List<Integer> hierarchy = createHierarchyForLevel(adjustedLevel);

            // 创建标题信息
            HeadingInfo heading = new HeadingInfo(current.text, current.styleId, adjustedLevel, current.paragraph);
            headings.add(0, heading); // 插入到开头，保持顺序

            System.out.println("找到有效标题: " + current.text + " (级别: " + adjustedLevel + ", 层级: " + hierarchy + ")");
        }
    }

    /**
     * 分析段落是否为标题候选
     */
    private static HeadingCandidate analyzeHeadingCandidate(String text, String styleId, XWPFParagraph paragraph) {
        HeadingCandidate candidate = new HeadingCandidate();
        candidate.text = text;
        candidate.styleId = styleId;
        candidate.isValidHeading = false;
        candidate.level = 0;

        // 1. 检查样式ID
        int styleLevel = getHeadingLevel(styleId);

        // 2. 检查文本模式
        int textLevel = analyzeTextHeadingLevel(text);

        // 3. 检查格式特征
        int formatLevel = analyzeFormatHeadingLevel(paragraph);

        // 综合判断
        if (styleLevel > 0) {
            candidate.level = styleLevel;
            candidate.isValidHeading = isValidHeadingByContent(text, styleLevel);
        } else if (textLevel > 0) {
            candidate.level = textLevel;
            candidate.isValidHeading = isValidHeadingByContent(text, textLevel);
        } else if (formatLevel > 0) {
            candidate.level = formatLevel;
            candidate.isValidHeading = isValidHeadingByContent(text, formatLevel);
        }

        return candidate;
    }

    /**
     * 标题候选信息类
     */
    static class HeadingCandidate {
        String text;
        String styleId;
        boolean isValidHeading;
        int level;
    }

    /**
     * 分析文本的标题级别
     */
    private static int analyzeTextHeadingLevel(String text) {
        // 0. 排除明显的段落内容（不是标题）
        if (isContentParagraph(text)) {
            return 0;
        }

        // 1. 第X章格式 - 一级标题
        if (text.matches("^第[一二三四五六七八九十百千万\\d]+[章篇部分].*")) {
            return 1;
        }

        // 2. 主要章节编号格式 - 一级或二级标题
        if (text.matches("^\\d+[、．.。].*")) {
            // 检查是否为主要章节（通常是较短的标题）
            if (text.length() < 50 && containsKeywords(text, new String[]{"管理", "措施", "方案", "计划", "制度", "要求", "目标", "保证"})) {
                return 2; // 二级标题
            }
            return 0; // 长段落内容，不是标题
        }

        // 3. 层级编号格式
        if (text.matches("^\\d+\\.\\d+.*")) {
            if (text.length() < 80 && !text.contains("。") && !text.contains("，")) {
                return 3; // 三级标题
            }
            return 0; // 长段落内容
        }

        if (text.matches("^\\d+\\.\\d+\\.\\d+.*")) {
            if (text.length() < 60) {
                return 4; // 四级标题
            }
            return 0;
        }

        // 4. 中文数字格式 - 智能识别标题
        if (text.matches("^[一二三四五六七八九十百千万]+[、．.。].*")) {
            // 更宽松的标题识别条件
            if (text.length() < 100 && isLikelyChineseNumberHeading(text)) {
                return 2;
            }
            return 0; // 段落内容
        }

        // 5. 括号格式 - 通常是三级标题，但要验证
        if (text.matches("^[（(【][一二三四五六七八九十百千万\\d]+[）)】].*")) {
            if (text.length() < 100 && !text.contains("。")) {
                return 3;
            }
            return 0; // 段落内容
        }

        // 6. 包含章节关键词的短标题
        if (text.matches(".*[章节部分篇].*") && text.length() < 30) {
            return 1;
        }

        return 0; // 不是标题格式
    }

    /**
     * 判断是否为段落内容（而非标题）
     */
    private static boolean isContentParagraph(String text) {
        // 1. 包含圆圈数字的通常是段落内容
        if (text.matches("^[①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳].*")) {
            return true;
        }

        // 2. 很长的文本通常是段落内容
        if (text.length() > 200) {
            return true;
        }

        // 3. 包含多个句号的通常是段落内容
        if (text.split("。").length > 3) {
            return true;
        }

        // 4. 包含具体描述性词汇的通常是段落内容
        String[] contentIndicators = {
            "应当", "必须", "严禁", "确保", "通过", "采用", "实施", "执行", "建立", "完善",
            "加强", "提高", "保证", "防止", "避免", "减少", "控制", "监督", "检查", "落实"
        };

        for (String indicator : contentIndicators) {
            if (text.contains(indicator) && text.length() > 50) {
                return true;
            }
        }

        return false;
    }

    /**
     * 智能判断中文数字开头的文本是否为标题
     */
    private static boolean isLikelyChineseNumberHeading(String text) {
        // 1. 包含常见标题关键词
        String[] titleKeywords = {
            "目标", "措施", "要求", "管理", "制度", "方案", "计划", "保证", "控制", "体系",
            "方针", "政策", "原则", "标准", "规范", "流程", "程序", "办法", "细则", "准备",
            "组织", "机构", "职责", "分工", "配置", "安排", "部署", "实施", "执行", "监督",
            "检查", "验收", "评定", "考核", "奖惩", "培训", "教育", "技术", "工艺", "质量",
            "安全", "环保", "文明", "进度", "成本", "资源", "材料", "设备", "人员", "资金"
        };

        for (String keyword : titleKeywords) {
            if (text.contains(keyword)) {
                return true;
            }
        }

        // 2. 短文本且不包含明显的段落特征
        if (text.length() < 30) {
            // 短文本通常是标题，除非包含明显的段落标识
            return !containsContentIndicators(text);
        }

        // 3. 中等长度文本的进一步判断
        if (text.length() < 60) {
            // 不包含句号且不包含段落特征词汇
            return !text.contains("。") && !containsContentIndicators(text);
        }

        return false;
    }

    /**
     * 检查是否包含段落内容指示词
     */
    private static boolean containsContentIndicators(String text) {
        String[] contentIndicators = {
            "应当", "必须", "严禁", "确保", "通过", "采用", "实施", "执行", "建立", "完善",
            "加强", "提高", "保证", "防止", "避免", "减少", "控制", "监督", "检查", "落实",
            "根据", "按照", "依据", "遵循", "符合", "满足", "达到", "实现", "完成", "做好"
        };

        for (String indicator : contentIndicators) {
            if (text.contains(indicator)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查文本是否包含关键词
     */
    private static boolean containsKeywords(String text, String[] keywords) {
        for (String keyword : keywords) {
            if (text.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 分析段落格式的标题级别
     */
    private static int analyzeFormatHeadingLevel(XWPFParagraph paragraph) {
        // 检查段落格式特征
        try {
            // 检查是否加粗
            boolean isBold = false;
            for (XWPFRun run : paragraph.getRuns()) {
                if (run.isBold()) {
                    isBold = true;
                    break;
                }
            }

            // 检查字体大小
            int fontSize = 0;
            for (XWPFRun run : paragraph.getRuns()) {
                if (run.getFontSize() > fontSize) {
                    fontSize = run.getFontSize();
                }
            }

            // 根据格式特征判断级别
            if (isBold && fontSize >= 16) {
                return 1; // 一级标题：加粗且字体较大
            } else if (isBold && fontSize >= 14) {
                return 2; // 二级标题：加粗且字体中等
            } else if (isBold) {
                return 3; // 三级标题：仅加粗
            }

        } catch (Exception e) {
            // 忽略格式分析错误
        }

        return 0; // 不是标题格式
    }

    /**
     * 根据内容验证标题有效性
     */
    private static boolean isValidHeadingByContent(String text, int level) {
        // 过滤掉目录标题
        if (text.equals("目录")) {
            return false;
        }

        // 过滤掉过短的标题
        if (text.length() < 2) {
            return false;
        }

        // 过滤掉纯数字的标题
        if (text.matches("^\\d+$")) {
            return false;
        }

        // 检查是否符合标题格式
        String matchedPattern = getMatchedHeadingPattern(text);
        if (matchedPattern != null) {
            return true;
        }

        // 宽松策略：如果有明确的级别，接受
        return level > 0;
    }

    /**
     * 验证层级关系是否有效
     */
    private static boolean isValidHierarchyLevel(int newLevel, List<Integer> currentHierarchy) {
        if (currentHierarchy.isEmpty()) {
            return true; // 第一个标题总是有效的
        }

        int lastLevel = currentHierarchy.get(currentHierarchy.size() - 1);

        // 允许的层级关系：
        // 1. 同级标题
        // 2. 下一级标题（递增1）
        // 3. 回到上级标题（任意减少）
        return newLevel <= lastLevel + 1;
    }

    /**
     * 根据上下文调整标题级别
     */
    private static int adjustHeadingLevelByContext(HeadingCandidate candidate, List<HeadingInfo> existingHeadings) {
        String text = candidate.text;
        int originalLevel = candidate.level;

        // 如果没有已存在的标题，保持原级别
        if (existingHeadings.isEmpty()) {
            return originalLevel;
        }

        // 获取最后一个1.x格式的标题
        HeadingInfo lastNumberDotHeading = findLastNumberDotHeading(existingHeadings);
        HeadingInfo lastHeading = existingHeadings.get(existingHeadings.size() - 1);

        // 特殊处理：数字.数字格式的标题（如1.1, 1.2）应该是同级的
        if (isNumberDotNumberFormat(text)) {
            if (lastNumberDotHeading != null) {
                String currentPrefix = getNumberPrefix(text);
                String lastPrefix = getNumberPrefix(lastNumberDotHeading.getText());

                // 如果主编号相同（如都是1.x），则应该是同级
                if (currentPrefix.equals(lastPrefix)) {
                    return lastNumberDotHeading.getLevel();
                } else {
                    // 不同主编号，可能是新的一级（如从1.x到2.x）
                    return lastNumberDotHeading.getLevel();
                }
            }
        }

        // 特殊处理：在1.x标题之后的其他标题应该是子级
        if (lastNumberDotHeading != null && !isNumberDotNumberFormat(text)) {
            // 中文数字标题（一、二、三）在1.x标题下应该是子级
            if (isChineseNumberFormat(text)) {
                return lastNumberDotHeading.getLevel() + 1;
            }

            // 阿拉伯数字标题（1、2、3）在1.x标题下应该是子级
            if (isArabicNumberFormat(text)) {
                // 检查是否在中文数字标题之后，如果是，则应该是中文数字标题的子级
                HeadingInfo lastChineseHeading = findLastChineseNumberHeading(existingHeadings);
                if (lastChineseHeading != null &&
                    lastChineseHeading != lastNumberDotHeading &&
                    isAfterHeading(lastChineseHeading, lastNumberDotHeading, existingHeadings)) {
                    return lastChineseHeading.getLevel() + 1;
                } else {
                    return lastNumberDotHeading.getLevel() + 1;
                }
            }

            // 其他格式的标题
            if (isBetweenNumberDotHeadings(text, existingHeadings)) {
                // 这个标题应该是1.x的子标题
                return lastNumberDotHeading.getLevel() + 1;
            }
        }

        return originalLevel;
    }

    /**
     * 查找最后一个中文数字格式的标题
     */
    private static HeadingInfo findLastChineseNumberHeading(List<HeadingInfo> headings) {
        for (int i = headings.size() - 1; i >= 0; i--) {
            HeadingInfo heading = headings.get(i);
            if (isChineseNumberFormat(heading.getText())) {
                return heading;
            }
        }
        return null;
    }

    /**
     * 检查heading1是否在heading2之后
     */
    private static boolean isAfterHeading(HeadingInfo heading1, HeadingInfo heading2, List<HeadingInfo> headings) {
        int index1 = headings.indexOf(heading1);
        int index2 = headings.indexOf(heading2);
        return index1 > index2;
    }

    /**
     * 查找最后一个数字.数字格式的标题
     */
    private static HeadingInfo findLastNumberDotHeading(List<HeadingInfo> headings) {
        for (int i = headings.size() - 1; i >= 0; i--) {
            HeadingInfo heading = headings.get(i);
            if (isNumberDotNumberFormat(heading.getText())) {
                return heading;
            }
        }
        return null;
    }

    /**
     * 检查当前标题是否在两个1.x标题之间
     */
    private static boolean isBetweenNumberDotHeadings(String currentText, List<HeadingInfo> existingHeadings) {
        // 简化判断：如果当前文本不是数字.数字格式，且存在1.x格式的标题，
        // 则假设这是1.x的子标题
        return !isNumberDotNumberFormat(currentText) && findLastNumberDotHeading(existingHeadings) != null;
    }

    /**
     * 判断是否为数字.数字格式（如1.1, 1.2）
     */
    private static boolean isNumberDotNumberFormat(String text) {
        return text.matches("^\\d+\\.\\d+.*");
    }

    /**
     * 获取数字前缀（如1.1返回"1"）
     */
    private static String getNumberPrefix(String text) {
        if (text.matches("^\\d+\\.\\d+.*")) {
            return text.split("\\.")[0];
        }
        return "";
    }

    /**
     * 判断是否为中文数字格式（一、二、三）
     */
    private static boolean isChineseNumberFormat(String text) {
        return text.matches("^[一二三四五六七八九十百千万]+[、．.。].*");
    }

    /**
     * 判断是否为阿拉伯数字格式（1、2、3）
     */
    private static boolean isArabicNumberFormat(String text) {
        return text.matches("^\\d+[、．.。].*");
    }

    /**
     * 检查是否有下一个数字.数字格式的标题
     */
    private static boolean hasNextNumberDotNumberHeading(String currentText, String lastHeadingText) {
        // 简化判断：如果当前文本不是数字.数字格式，且上一个是，
        // 则假设这是中间的子标题
        return !isNumberDotNumberFormat(currentText) && isNumberDotNumberFormat(lastHeadingText);
    }

    /**
     * 更新当前层级
     */
    private static void updateHierarchy(List<Integer> currentHierarchy, int newLevel) {
        // 移除比新级别高的所有级别
        currentHierarchy.removeIf(level -> level >= newLevel);

        // 添加新级别
        currentHierarchy.add(newLevel);
    }

    /**
     * 判断是否为有效的标题
     */
    private static boolean isValidHeading(String text) {
        System.out.println("  检查标题有效性: \"" + text + "\"");
        
        // 过滤掉目录标题
        if (text.equals("目录")) {
            System.out.println("    -> 跳过：目录标题");
            return false;
        }

        // 过滤掉空白或过短的标题
        if (text.length() < 2) {
            System.out.println("    -> 跳过：标题过短 (长度: " + text.length() + ")");
            return false;
        }

        // 过滤掉纯数字的标题（但允许单个数字后面有内容的标题）
        if (text.matches("^\\d+$")) {
            System.out.println("    -> 跳过：纯数字标题");
            return false;
        }

        // 检查各种标题格式
        String matchedPattern = getMatchedHeadingPattern(text);
        if (matchedPattern != null) {
            System.out.println("    -> 接受：匹配格式 [" + matchedPattern + "]");
            return true;
        }

        // 宽松策略：如果标题被识别为标题样式，但不符合上述格式，也接受
        System.out.println("    -> 接受：宽松策略（标题样式但格式特殊）");
        return true;
    }

    /**
     * 获取匹配的标题格式类型
     */
    private static String getMatchedHeadingPattern(String text) {
        // 1. 包含"章"、"节"、"部分"等关键词
        if (text.matches(".*[章节部分篇].*")) {
            return "章节关键词";
        }

        // 2. 数字开头的标题：1、1.、1.1、1.1.1等
        if (text.matches("^\\d+[、．.。].*")) {
            return "数字+标点";
        }
        if (text.matches("^\\d+(\\.\\d+)*\\s.*")) {
            return "多级数字编号";
        }

        // 3. 中文数字开头的标题：一、二、三等
        if (text.matches("^[一二三四五六七八九十百千万]+[、．.。].*")) {
            return "中文数字+标点";
        }

        // 4. 括号格式的标题：（一）、(1)、【1】等
        if (text.matches("^[（(【][一二三四五六七八九十百千万\\d]+[）)】].*")) {
            return "括号编号";
        }

        // 5. 第X章、第X节等格式
        if (text.matches("^第[一二三四五六七八九十百千万\\d]+[章节部分篇].*")) {
            return "第X章格式";
        }

        // 6. 英文字母开头的标题：A、B、C等
        if (text.matches("^[A-Za-z]+[、．.。].*")) {
            return "英文字母+标点";
        }

        // 7. 罗马数字：I、II、III等
        if (text.matches("^[IVXLCDMivxlcdm]+[、．.。].*")) {
            return "罗马数字+标点";
        }

        return null; // 没有匹配的格式
    }

    /**
     * 判断样式是否为标题样式，返回标题级别
     */
    private static int getHeadingLevel(String styleId) {
        if (styleId == null) return 0;

        String lowerStyleId = styleId.toLowerCase();
        
        // 支持标准的Heading1-Heading6样式
        if (lowerStyleId.contains("heading1") || lowerStyleId.equals("1")) {
            return 1;
        } else if (lowerStyleId.contains("heading2") || lowerStyleId.equals("2")) {
            return 2;
        } else if (lowerStyleId.contains("heading3") || lowerStyleId.equals("3")) {
            return 3;
        } else if (lowerStyleId.contains("heading4") || lowerStyleId.equals("4")) {
            return 4;
        } else if (lowerStyleId.contains("heading5") || lowerStyleId.equals("5")) {
            return 5;
        } else if (lowerStyleId.contains("heading6") || lowerStyleId.equals("6")) {
            return 6;
        }

        // 支持其他可能的标题样式命名
        if (lowerStyleId.contains("title") || lowerStyleId.contains("标题")) {
            return 1; // 默认作为一级标题
        }

        return 0;
    }

    /**
     * 3. 为标题添加书签
     */
    private static void addBookmarksToHeadings(List<HeadingInfo> headings) {
        System.out.println("开始为标题添加书签，共 " + headings.size() + " 个标题");
        
        long bookmarkId = 1; // 从1开始，避免ID冲突
        int successCount = 0;
        int failCount = 0;

        for (int i = 0; i < headings.size(); i++) {
            HeadingInfo heading = headings.get(i);
            XWPFParagraph paragraph = heading.getParagraph();
            String bookmarkName = heading.getBookmarkName();

            System.out.println("处理书签 " + (i + 1) + "/" + headings.size() + ": \"" + heading.getText() + "\"");
            System.out.println("  书签名称: " + bookmarkName);

            try {
                // 清除段落中可能存在的旧书签
                clearExistingBookmarks(paragraph);

                // 添加书签开始标记（在段落开头）
                CTBookmark bookmarkStart = paragraph.getCTP().insertNewBookmarkStart(0);
                bookmarkStart.setName(bookmarkName);
                bookmarkStart.setId(BigInteger.valueOf(bookmarkId));

                // 添加书签结束标记（在段落末尾）
                CTMarkupRange bookmarkEnd = paragraph.getCTP().addNewBookmarkEnd();
                bookmarkEnd.setId(BigInteger.valueOf(bookmarkId));

                bookmarkId++;
                successCount++;

                System.out.println("  ✓ 书签添加成功: " + heading.getText() + " -> " + bookmarkName);
            } catch (Exception e) {
                failCount++;
                System.out.println("  ✗ 书签添加失败: " + heading.getText() + " - " + e.getMessage());
                e.printStackTrace();
            }
        }

        System.out.println("书签添加完成统计:");
        System.out.println("  总标题数: " + headings.size());
        System.out.println("  成功添加: " + successCount);
        System.out.println("  添加失败: " + failCount);
    }

    /**
     * 清除段落中现有的书签
     */
    private static void clearExistingBookmarks(XWPFParagraph paragraph) {
        try {
            // 移除现有的书签开始标记
            for (int i = paragraph.getCTP().getBookmarkStartList().size() - 1; i >= 0; i--) {
                paragraph.getCTP().removeBookmarkStart(i);
            }
            // 移除现有的书签结束标记
            for (int i = paragraph.getCTP().getBookmarkEndList().size() - 1; i >= 0; i--) {
                paragraph.getCTP().removeBookmarkEnd(i);
            }
        } catch (Exception e) {
            // 忽略清除错误
        }
    }

    /**
     * 4. 计算临时页码（用于初始目录生成）
     */
    private static void calculateTemporaryPageNumbers(List<HeadingInfo> headings) {
        int currentPage = 1; // 临时从第1页开始

        for (HeadingInfo heading : headings) {
            if (heading.getLevel() == 1) {
                // 一级标题，新的一页
                currentPage++;
                heading.setPageNumber(currentPage);
            } else {
                // 二级标题，在当前页
                heading.setPageNumber(currentPage);
            }
        }
        System.out.println("临时页码计算完成");
    }

    /**
     * 计算目录占用的页数（更精确的计算）
     */
    private static int calculateTocPages(XWPFDocument document, int headingCount) {
        // 更精确的估算：
        // 1. 目录标题：1行
        // 2. 空行：1行
        // 3. 目录条目：headingCount行
        // 4. 分页符：1行
        // 假设每页可以容纳约45行（考虑页边距）
        int linesPerPage = 45;
        int totalLines = 1 + 1 + headingCount + 1; // 标题 + 空行 + 条目 + 分页符
        int tocPages = (int) Math.ceil((double) totalLines / linesPerPage);

        // 至少1页
        if (tocPages < 1) tocPages = 1;

        System.out.println("估算目录页数: " + tocPages + " 页 (共 " + headingCount + " 个条目, " + totalLines + " 行)");
        return tocPages;
    }

    /**
     * 根据目录页数重新计算页码
     */
    private static void recalculatePageNumbers(List<HeadingInfo> headings, int tocPages) {
        int currentPage = tocPages; // 内容从目录后开始

        for (HeadingInfo heading : headings) {
            if (heading.getLevel() == 1) {
                // 一级标题，新的一页
                currentPage++;
                heading.setPageNumber(currentPage);
            } else {
                // 二级标题，在当前页
                heading.setPageNumber(currentPage);
            }

            System.out.println("重新计算页码: " + heading.getText() + " -> 第" + heading.getPageNumber() + "页");
        }
    }

    /**
     * 5. 在文档最初的位置插入目录
     */
    private static void insertTableOfContents(XWPFDocument document, List<HeadingInfo> headings) {
        System.out.println("开始插入目录，共 " + headings.size() + " 个标题");
        
        if (headings.isEmpty()) {
            System.out.println("没有找到标题，无法生成目录");
            return;
        }

        try {
            // 获取第一个段落的位置
            XWPFParagraph firstParagraph = document.getParagraphs().get(0);

            // 在第一个段落前插入目录标题
            XWPFParagraph tocTitleParagraph = document.insertNewParagraph(firstParagraph.getCTP().newCursor());
            tocTitleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun tocTitleRun = tocTitleParagraph.createRun();
            tocTitleRun.setText("目录");
            tocTitleRun.setBold(true);
            tocTitleRun.setFontSize(16);
            tocTitleRun.setFontFamily("宋体");
            System.out.println("目录标题插入成功");

            // 添加空行
            XWPFParagraph emptyLine = document.insertNewParagraph(firstParagraph.getCTP().newCursor());
            System.out.println("空行插入成功");

            // 为每个标题创建目录条目
            int successCount = 0;
            int failCount = 0;
            
            for (int i = 0; i < headings.size(); i++) {
                HeadingInfo heading = headings.get(i);
                System.out.println("处理标题 " + (i + 1) + "/" + headings.size() + ": \"" + heading.getText() + "\"");
                
                try {
                    XWPFParagraph tocEntry = document.insertNewParagraph(firstParagraph.getCTP().newCursor());
                    createTocEntryInParagraph(tocEntry, heading);
                    successCount++;
                    System.out.println("  ✓ 标题 " + (i + 1) + " 处理成功");
                } catch (Exception e) {
                    failCount++;
                    System.out.println("  ✗ 标题 " + (i + 1) + " 处理失败: " + e.getMessage());
                    e.printStackTrace();
                    // 继续处理下一个标题，不中断整个过程
                }
            }

            // 添加分页符
            try {
                XWPFParagraph pageBreak = document.insertNewParagraph(firstParagraph.getCTP().newCursor());
                XWPFRun pageBreakRun = pageBreak.createRun();
                pageBreakRun.addBreak(BreakType.PAGE);
                System.out.println("分页符插入成功");
            } catch (Exception e) {
                System.out.println("分页符插入失败: " + e.getMessage());
            }

            System.out.println("目录插入完成统计:");
            System.out.println("  总标题数: " + headings.size());
            System.out.println("  成功处理: " + successCount);
            System.out.println("  处理失败: " + failCount);
            
        } catch (Exception e) {
            System.out.println("目录插入过程发生严重错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 更新目录中的页码
     */
    private static void updateTocPageNumbers(XWPFDocument document, List<HeadingInfo> headings) {
        System.out.println("开始更新目录页码...");

        // 找到所有目录段落并更新页码
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int headingIndex = 0;

        for (XWPFParagraph paragraph : paragraphs) {
            // 检查段落是否包含超链接（更简单的检测方法）
            if (containsHyperlink(paragraph) && headingIndex < headings.size()) {
                HeadingInfo heading = headings.get(headingIndex);
                updateParagraphPageNumber(paragraph, heading.getPageNumber());
                System.out.println("更新页码: " + heading.getText() + " -> 第" + heading.getPageNumber() + "页");
                headingIndex++;
            }
        }

        System.out.println("目录页码更新完成");
    }

    /**
     * 检查段落是否包含超链接
     */
    private static boolean containsHyperlink(XWPFParagraph paragraph) {
        // 检查段落是否包含超链接运行
        for (XWPFRun run : paragraph.getRuns()) {
            if (run instanceof XWPFHyperlinkRun) {
                return true;
            }
        }

        // 检查段落文本是否包含标题文本（作为备选检测方法）
        String paragraphText = paragraph.getText();
        if (paragraphText != null && (paragraphText.contains("章") || paragraphText.contains("."))) {
            return true;
        }

        return false;
    }

    /**
     * 更新段落中的页码
     */
    private static void updateParagraphPageNumber(XWPFParagraph paragraph, int newPageNumber) {
        // 找到段落中最后一个文本运行（通常是页码）
        List<XWPFRun> runs = paragraph.getRuns();
        if (!runs.isEmpty()) {
            // 从后往前查找数字（页码）
            for (int i = runs.size() - 1; i >= 0; i--) {
                XWPFRun run = runs.get(i);
                String text = run.getText(0);
                if (text != null && text.matches("\\d+")) {
                    run.setText(String.valueOf(newPageNumber), 0);
                    System.out.println("  页码已更新: " + text + " -> " + newPageNumber);
                    return;
                }
            }

            // 如果没找到数字，在最后添加页码
            XWPFRun lastRun = runs.get(runs.size() - 1);
            String currentText = lastRun.getText(0);
            if (currentText != null && !currentText.matches("\\d+")) {
                lastRun.setText(String.valueOf(newPageNumber), 0);
                System.out.println("  添加页码: " + newPageNumber);
            }
        }
    }

    /**
     * 在指定段落中创建目录条目
     */
    private static void createTocEntryInParagraph(XWPFParagraph tocParagraph, HeadingInfo heading) {
        System.out.println("开始创建目录条目: \"" + heading.getText() + "\" (级别: " + heading.getLevel() + ")");
        
        try {
            // 设置缩进（根据标题级别设置不同的缩进）
            int indentationValue = 0;
            if (heading.getLevel() > 2) {
                // 2级标题不缩进，3级标题缩进1级，4级标题缩进2级，以此类推
                // 每级缩进420单位（约0.3英寸），使层次更清晰
                indentationValue = 420 * (heading.getLevel() - 2);
                tocParagraph.setIndentationLeft(indentationValue);
                System.out.println("  设置缩进: " + indentationValue + " 单位 (级别: " + heading.getLevel() + ")");
            } else {
                System.out.println("  无缩进 (级别: " + heading.getLevel() + ")");
            }

            // 设置制表位（右对齐，点状引导线）
            try {
                CTPPr pPr = tocParagraph.getCTP().isSetPPr() ? tocParagraph.getCTP().getPPr() : tocParagraph.getCTP().addNewPPr();
                CTTabs tabs = pPr.isSetTabs() ? pPr.getTabs() : pPr.addNewTabs();

                // 清除现有制表位
                if (tabs.getTabList().size() > 0) {
                    tabs.setTabArray(null);
                }

                // 设置制表位：右对齐，点状引导线（虚线）
                // 制表位位置固定在右边距，不受左侧缩进影响
                CTTabStop tabStop = tabs.addNewTab();
                tabStop.setVal(STTabJc.RIGHT);
                tabStop.setLeader(STTabTlc.DOT); // 点状引导线（虚线）
                tabStop.setPos(BigInteger.valueOf(8500)); // 制表位位置固定在右边距
                System.out.println("  制表位设置成功，位置: 8500");
            } catch (Exception e) {
                System.out.println("  ⚠ 制表位设置失败: " + e.getMessage());
            }

            // 创建超链接到书签
            try {
                XWPFHyperlinkRun hyperlinkRun = tocParagraph.createHyperlinkRun("#" + heading.getBookmarkName());
                hyperlinkRun.setText(heading.getText());
                hyperlinkRun.setFontFamily("宋体");
                
                // 根据标题级别设置不同的字体大小
                int fontSize = Math.max(10, 14 - heading.getLevel()); // 一级标题12号，二级11号，以此类推，最小10号
                hyperlinkRun.setFontSize(fontSize);
                hyperlinkRun.setColor("0000FF"); // 蓝色
                hyperlinkRun.setUnderline(UnderlinePatterns.SINGLE); // 下划线

                System.out.println("  超链接创建成功: " + heading.getText() + " -> " + heading.getBookmarkName());
            } catch (Exception e) {
                System.out.println("  ✗ 超链接创建失败: " + e.getMessage());
                e.printStackTrace();
                throw e; // 重新抛出异常，因为这是关键步骤
            }

            // 添加制表符（这会触发虚线填充）
            try {
                XWPFRun tabRun = tocParagraph.createRun();
                tabRun.addTab();
                System.out.println("  制表符添加成功");
            } catch (Exception e) {
                System.out.println("  ⚠ 制表符添加失败: " + e.getMessage());
            }

            // 添加页码
            try {
                XWPFRun pageRun = tocParagraph.createRun();
                pageRun.setText(String.valueOf(heading.getPageNumber()));
                pageRun.setFontFamily("宋体");
                int fontSize = Math.max(10, 14 - heading.getLevel());
                pageRun.setFontSize(fontSize); // 页码字体大小与标题一致
                System.out.println("  页码添加成功: 第" + heading.getPageNumber() + "页");
            } catch (Exception e) {
                System.out.println("  ⚠ 页码添加失败: " + e.getMessage());
            }

            System.out.println("  ✓ 目录条目创建完成: \"" + heading.getText() + "\"");

        } catch (Exception e) {
            System.out.println("  ✗ 目录条目创建失败: \"" + heading.getText() + "\" - " + e.getMessage());
            e.printStackTrace();
            // 不重新抛出异常，继续处理下一个标题
        }
    }



    /**
     * 6. 保存文档
     */
    private static void saveDocument(XWPFDocument document, String docPath) throws Exception {
        try (FileOutputStream fos = new FileOutputStream(docPath)) {
            document.write(fos);
        }
        document.close();
        System.out.println("文档已保存: " + docPath);
    }
}