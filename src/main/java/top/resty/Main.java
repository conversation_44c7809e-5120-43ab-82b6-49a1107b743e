package top.resty;

import java.io.FileOutputStream;
import java.math.BigInteger;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.apache.poi.xwpf.model.XWPFHeaderFooterPolicy;

public class Main {


  // 创建目录的方法
  private static void createTableOfContents(XWPFDocument doc) {
    // 创建目录标题
    XWPFParagraph tocTitleParagraph = doc.createParagraph();
    tocTitleParagraph.setAlignment(ParagraphAlignment.CENTER);
    XWPFRun tocTitleRun = tocTitleParagraph.createRun();
    tocTitleRun.setText("目录");
    tocTitleRun.setBold(true);
    tocTitleRun.setFontSize(16);
    tocTitleRun.setFontFamily("宋体");

    // 创建目录段落
    XWPFParagraph tocParagraph = doc.createParagraph();
    CTP ctP = tocParagraph.getCTP();

    // 创建复杂字段开始
    CTR fldChar1 = ctP.addNewR();
    CTFldChar fldCharBegin = fldChar1.addNewFldChar();
    fldCharBegin.setFldCharType(STFldCharType.BEGIN);

    // 创建字段指令
    CTR instrText = ctP.addNewR();
    CTText ctText = instrText.addNewInstrText();
    ctText.setStringValue(" TOC \\o \"1-3\" \\h \\z \\u ");
    ctText.setSpace(STOnOff.PRESERVE);

    // 创建复杂字段结束
    CTR fldChar2 = ctP.addNewR();
    CTFldChar fldCharEnd = fldChar2.addNewFldChar();
    fldCharEnd.setFldCharType(STFldCharType.END);

    // 添加目录占位文本
    XWPFRun tocRun = tocParagraph.createRun();
    tocRun.setText("请在Word中右键点击此处并选择更新域来生成目录");
    tocRun.setItalic(true);
    tocRun.setColor("808080");
  }

  public static void main(String[] args) {
    try (XWPFDocument doc = new XWPFDocument()) {


      // 创建文档标题
      XWPFParagraph titleParagraph = doc.createParagraph();
      titleParagraph.setAlignment(ParagraphAlignment.CENTER);
      XWPFRun titleRun = titleParagraph.createRun();
      titleRun.setText("文档标题");
      titleRun.setBold(true);
      titleRun.setFontSize(20);
      titleRun.setFontFamily("宋体");

      // 添加空行
      doc.createParagraph();

      // 创建目录
      createTableOfContents(doc);

      // 添加分页符
      XWPFParagraph pageBreakParagraph = doc.createParagraph();
      XWPFRun pageBreakRun = pageBreakParagraph.createRun();
      pageBreakRun.addBreak(BreakType.PAGE);

      // 创建20个一级标题，每个一级标题下有5个二级标题
      for (int i = 1; i <= 20; i++) {
        // 创建一级标题
        XWPFParagraph h1Paragraph = doc.createParagraph();
        h1Paragraph.setStyle("Heading1");
        XWPFRun h1Run = h1Paragraph.createRun();
        h1Run.setText("第" + i + "章 一级标题" + i);
        h1Run.setBold(true);
        h1Run.setFontSize(16);
        h1Run.setFontFamily("宋体");

        // 在一级标题下添加一些内容
        XWPFParagraph contentParagraph = doc.createParagraph();
        XWPFRun contentRun = contentParagraph.createRun();
        contentRun.setText("这是第" + i + "章的内容介绍。");
        contentRun.setFontFamily("宋体");

        // 创建5个二级标题
        for (int j = 1; j <= 5; j++) {
          XWPFParagraph h2Paragraph = doc.createParagraph();
          h2Paragraph.setStyle("Heading2");
          XWPFRun h2Run = h2Paragraph.createRun();
          h2Run.setText(i + "." + j + " 二级标题" + i + "." + j);
          h2Run.setBold(true);
          h2Run.setFontSize(14);
          h2Run.setFontFamily("宋体");

          // 在二级标题下添加一些内容
          XWPFParagraph subContentParagraph = doc.createParagraph();
          XWPFRun subContentRun = subContentParagraph.createRun();
          subContentRun.setText("这是第" + i + "章第" + j + "节的具体内容。在这里可以添加详细的说明和描述。");
          subContentRun.setFontFamily("宋体");

          // 添加空行
          doc.createParagraph();
        }

        // 在每章之间添加分页符（除了最后一章）
        if (i < 20) {
          XWPFParagraph chapterBreakParagraph = doc.createParagraph();
          XWPFRun chapterBreakRun = chapterBreakParagraph.createRun();
          chapterBreakRun.addBreak(BreakType.PAGE);
        }
      }

      // 保存文档
      try (FileOutputStream out = new FileOutputStream("generated_document.docx")) {
        doc.write(out);
        System.out.println("文档已成功生成：generated_document.docx");
      }
    } catch (Exception e) {
      e.printStackTrace();
    }
  }
}