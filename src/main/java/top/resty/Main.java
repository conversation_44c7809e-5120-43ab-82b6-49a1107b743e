package top.resty;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.math.BigInteger;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

public class Main {

    // 标题信息类
    static class HeadingInfo {
        private String text;
        private String styleId;
        private int level;
        private XWPFParagraph paragraph;
        private String bookmarkName;
        private int pageNumber;

        public HeadingInfo(String text, String styleId, int level, XWPFParagraph paragraph) {
            this.text = text;
            this.styleId = styleId;
            this.level = level;
            this.paragraph = paragraph;
            this.bookmarkName = "heading_" + System.currentTimeMillis() + "_" + Math.random();
            this.pageNumber = 1; // 默认页码
        }

        // Getters and Setters
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public String getStyleId() { return styleId; }
        public void setStyleId(String styleId) { this.styleId = styleId; }
        public int getLevel() { return level; }
        public void setLevel(int level) { this.level = level; }
        public XWPFParagraph getParagraph() { return paragraph; }
        public void setParagraph(XWPFParagraph paragraph) { this.paragraph = paragraph; }
        public String getBookmarkName() { return bookmarkName; }
        public void setBookmarkName(String bookmarkName) { this.bookmarkName = bookmarkName; }
        public int getPageNumber() { return pageNumber; }
        public void setPageNumber(int pageNumber) { this.pageNumber = pageNumber; }
    }

    public static void main(String[] args) {
        try {
            String docPath = "generated_document.docx";

            // 1. 获取文档内容，如果不存在则创建示例文档
            XWPFDocument document;
            try {
                document = loadDocument(docPath);
            } catch (Exception e) {
                System.out.println("文档不存在，创建示例文档...");
                document = createSampleDocument();
                saveDocument(document, docPath);
                document = loadDocument(docPath);
            }

            // 2. 遍历出文档中所有的标题并存进集合
            List<HeadingInfo> headings = extractHeadings(document);

            if (headings.isEmpty()) {
                System.out.println("文档中没有标题，创建示例文档...");
                document.close();
                document = createSampleDocument();
                saveDocument(document, docPath);
                document = loadDocument(docPath);
                headings = extractHeadings(document);
            }

            // 3. 为标题添加书签
            addBookmarksToHeadings(headings);

            // 4. 先用临时页码插入目录
            calculateTemporaryPageNumbers(headings);

            // 5. 在文档最初的位置插入目录
            insertTableOfContents(document, headings);

            // 6. 计算目录实际占用的页数，重新计算页码
            int tocPages = calculateTocPages(document, headings.size());
            recalculatePageNumbers(headings, tocPages);

            // 7. 更新目录中的页码
            updateTocPageNumbers(document, headings);

            // 8. 保存文档
            saveDocument(document, docPath);

            System.out.println("目录生成成功！");
            System.out.println("找到 " + headings.size() + " 个标题");
            System.out.println("目录占用 " + tocPages + " 页");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建示例文档
     */
    private static XWPFDocument createSampleDocument() {
        XWPFDocument document = new XWPFDocument();

        // 创建文档标题
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText("文档标题");
        titleRun.setBold(true);
        titleRun.setFontSize(20);
        titleRun.setFontFamily("宋体");

        // 添加空行
        document.createParagraph();

        // 创建20个一级标题，每个一级标题下有5个二级标题
        for (int i = 1; i <= 20; i++) {
            // 创建一级标题
            XWPFParagraph h1Paragraph = document.createParagraph();
            h1Paragraph.setStyle("Heading1");
            XWPFRun h1Run = h1Paragraph.createRun();
            h1Run.setText("第" + i + "章 一级标题" + i);
            h1Run.setBold(true);
            h1Run.setFontSize(16);
            h1Run.setFontFamily("宋体");

            // 在一级标题下添加内容
            XWPFParagraph contentParagraph = document.createParagraph();
            XWPFRun contentRun = contentParagraph.createRun();
            contentRun.setText("这是第" + i + "章的内容介绍。本章将详细介绍相关主题的各个方面。");
            contentRun.setFontFamily("宋体");
            contentRun.setFontSize(12);

            // 添加空行
            document.createParagraph();

            // 创建5个二级标题
            for (int j = 1; j <= 5; j++) {
                XWPFParagraph h2Paragraph = document.createParagraph();
                h2Paragraph.setStyle("Heading2");
                XWPFRun h2Run = h2Paragraph.createRun();
                h2Run.setText(i + "." + j + " 二级标题" + i + "." + j);
                h2Run.setBold(true);
                h2Run.setFontSize(14);
                h2Run.setFontFamily("宋体");

                // 在二级标题下添加内容
                XWPFParagraph subContentParagraph = document.createParagraph();
                XWPFRun subContentRun = subContentParagraph.createRun();
                subContentRun.setText("这是第" + i + "章第" + j + "节的具体内容。在这里可以添加详细的说明和描述，包括相关的技术细节、实例分析和应用场景等。");
                subContentRun.setFontFamily("宋体");
                subContentRun.setFontSize(12);

                // 添加空行
                document.createParagraph();
            }

            // 在每章之间添加分页符（除了最后一章）
            if (i < 20) {
                XWPFParagraph chapterBreakParagraph = document.createParagraph();
                XWPFRun chapterBreakRun = chapterBreakParagraph.createRun();
                chapterBreakRun.addBreak(BreakType.PAGE);
            }
        }

        System.out.println("示例文档创建完成");
        return document;
    }

    /**
     * 1. 加载文档
     */
    private static XWPFDocument loadDocument(String docPath) throws Exception {
        try (FileInputStream fis = new FileInputStream(docPath)) {
            return new XWPFDocument(fis);
        }
    }

    /**
     * 2. 遍历文档中所有的标题并存进集合
     */
    private static List<HeadingInfo> extractHeadings(XWPFDocument document) {
        List<HeadingInfo> headings = new ArrayList<>();

        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String styleId = paragraph.getStyleID();
            String text = paragraph.getText();

            if (text != null && !text.trim().isEmpty() && styleId != null) {
                int level = getHeadingLevel(styleId);
                if (level > 0) {
                    HeadingInfo heading = new HeadingInfo(text.trim(), styleId, level, paragraph);
                    headings.add(heading);
                    System.out.println("找到标题: " + text + " (级别: " + level + ")");
                }
            }
        }

        return headings;
    }

    /**
     * 判断样式是否为标题样式，返回标题级别
     */
    private static int getHeadingLevel(String styleId) {
        if (styleId == null) return 0;

        String lowerStyleId = styleId.toLowerCase();
        if (lowerStyleId.contains("heading1") || lowerStyleId.contains("1")) {
            return 1;
        } else if (lowerStyleId.contains("heading2") || lowerStyleId.contains("2")) {
            return 2;
        } else if (lowerStyleId.contains("heading3") || lowerStyleId.contains("3")) {
            return 3;
        }

        return 0;
    }

    /**
     * 3. 为标题添加书签
     */
    private static void addBookmarksToHeadings(List<HeadingInfo> headings) {
        long bookmarkId = 0;

        for (HeadingInfo heading : headings) {
            XWPFParagraph paragraph = heading.getParagraph();
            String bookmarkName = heading.getBookmarkName();

            // 添加书签开始标记
            CTBookmark bookmarkStart = paragraph.getCTP().addNewBookmarkStart();
            bookmarkStart.setName(bookmarkName);
            bookmarkStart.setId(BigInteger.valueOf(bookmarkId));

            // 添加书签结束标记
            CTMarkupRange bookmarkEnd = paragraph.getCTP().addNewBookmarkEnd();
            bookmarkEnd.setId(BigInteger.valueOf(bookmarkId));

            bookmarkId++;

            System.out.println("为标题添加书签: " + heading.getText() + " -> " + bookmarkName);
        }
    }

    /**
     * 4. 计算页码（简单估算）
     */
    private static void calculatePageNumbers(List<HeadingInfo> headings) {
        int currentPage = 1; // 从第1页开始（第1页是目录，第2页开始是内容）

        for (HeadingInfo heading : headings) {
            if (heading.getLevel() == 1) {
                // 一级标题，新的一页
                currentPage++;
                heading.setPageNumber(currentPage);
            } else {
                // 二级标题，在当前页
                heading.setPageNumber(currentPage);
            }

            System.out.println("标题页码: " + heading.getText() + " -> 第" + heading.getPageNumber() + "页");
        }
    }

    /**
     * 5. 在文档最初的位置插入目录
     */
    private static void insertTableOfContents(XWPFDocument document, List<HeadingInfo> headings) {
        if (headings.isEmpty()) {
            System.out.println("没有找到标题，无法生成目录");
            return;
        }

        // 创建目录段落列表
        List<XWPFParagraph> tocParagraphs = new ArrayList<>();

        // 创建目录标题
        XWPFParagraph tocTitleParagraph = document.createParagraph();
        tocTitleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun tocTitleRun = tocTitleParagraph.createRun();
        tocTitleRun.setText("目录");
        tocTitleRun.setBold(true);
        tocTitleRun.setFontSize(16);
        tocTitleRun.setFontFamily("宋体");
        tocParagraphs.add(tocTitleParagraph);

        // 添加空行
        XWPFParagraph emptyLine = document.createParagraph();
        tocParagraphs.add(emptyLine);

        // 为每个标题创建目录条目
        for (HeadingInfo heading : headings) {
            XWPFParagraph tocEntry = createTocEntry(document, heading);
            tocParagraphs.add(tocEntry);
        }

        // 添加分页符
        XWPFParagraph pageBreak = document.createParagraph();
        XWPFRun pageBreakRun = pageBreak.createRun();
        pageBreakRun.addBreak(BreakType.PAGE);
        tocParagraphs.add(pageBreak);

        // 使用更简单的方法：直接在文档开头插入所有目录段落
        insertParagraphsAtBeginning(document, tocParagraphs);

        System.out.println("目录插入完成，共 " + headings.size() + " 个条目");
    }

    /**
     * 创建单个目录条目
     */
    private static XWPFParagraph createTocEntry(XWPFDocument document, HeadingInfo heading) {
        XWPFParagraph tocParagraph = document.createParagraph();

        // 设置缩进（二级标题及以下缩进）
        if (heading.getLevel() > 1) {
            tocParagraph.setIndentationLeft(400 * (heading.getLevel() - 1));
        }

        // 设置制表位（右对齐，点状引导线）
        CTPPr pPr = tocParagraph.getCTP().isSetPPr() ? tocParagraph.getCTP().getPPr() : tocParagraph.getCTP().addNewPPr();
        CTTabs tabs = pPr.isSetTabs() ? pPr.getTabs() : pPr.addNewTabs();

        // 清除现有制表位
        if (tabs.getTabList().size() > 0) {
            tabs.setTabArray(null);
        }

        // 设置制表位：右对齐，点状引导线（虚线）
        CTTabStop tabStop = tabs.addNewTab();
        tabStop.setVal(STTabJc.RIGHT);
        tabStop.setLeader(STTabTlc.DOT); // 点状引导线（虚线）
        tabStop.setPos(BigInteger.valueOf(8500)); // 制表位位置（调整为更合适的位置）

        // 创建内部超链接到书签
        try {
            // 方法1：使用PAGEREF字段创建可跳转的链接
            // 创建字段开始
            XWPFRun fieldStart = tocParagraph.createRun();
            fieldStart.getCTR().addNewFldChar().setFldCharType(STFldCharType.BEGIN);

            // 创建HYPERLINK字段指令
            XWPFRun fieldInstr = tocParagraph.createRun();
            CTText instrText = fieldInstr.getCTR().addNewInstrText();
            instrText.setStringValue(" HYPERLINK \\l \"" + heading.getBookmarkName() + "\" ");
            instrText.setSpace(org.apache.xmlbeans.impl.xb.xmlschema.SpaceAttribute.Space.PRESERVE);

            // 创建字段分隔符
            XWPFRun fieldSeparate = tocParagraph.createRun();
            fieldSeparate.getCTR().addNewFldChar().setFldCharType(STFldCharType.SEPARATE);

            // 创建显示文本（超链接的可见文本）
            XWPFRun linkText = tocParagraph.createRun();
            linkText.setText(heading.getText());
            linkText.setFontFamily("宋体");
            linkText.setFontSize(12);
            linkText.setColor("0000FF"); // 蓝色
            linkText.setUnderline(UnderlinePatterns.SINGLE); // 下划线

            // 创建字段结束
            XWPFRun fieldEnd = tocParagraph.createRun();
            fieldEnd.getCTR().addNewFldChar().setFldCharType(STFldCharType.END);

            System.out.println("创建HYPERLINK字段: " + heading.getText() + " -> " + heading.getBookmarkName());

        } catch (Exception e) {
            // 如果超链接创建失败，使用普通文本
            System.out.println("创建超链接失败，使用普通文本: " + e.getMessage());
            XWPFRun titleRun = tocParagraph.createRun();
            titleRun.setText(heading.getText());
            titleRun.setFontFamily("宋体");
            titleRun.setFontSize(12);
            titleRun.setColor("0000FF");
            titleRun.setUnderline(UnderlinePatterns.SINGLE);
        }

        // 添加制表符（这会触发虚线填充）
        XWPFRun tabRun = tocParagraph.createRun();
        tabRun.addTab();

        // 添加页码
        XWPFRun pageRun = tocParagraph.createRun();
        pageRun.setText(String.valueOf(heading.getPageNumber()));
        pageRun.setFontFamily("宋体");
        pageRun.setFontSize(12);

        System.out.println("创建目录条目: " + heading.getText() + " -> 第" + heading.getPageNumber() + "页");

        return tocParagraph;
    }

    /**
     * 在文档开头插入段落列表
     */
    private static void insertParagraphsAtBeginning(XWPFDocument document, List<XWPFParagraph> paragraphs) {
        try {
            // 获取文档的第一个段落
            if (document.getParagraphs().size() > 0) {
                XWPFParagraph firstParagraph = document.getParagraphs().get(0);

                // 正序插入段落（修复倒序问题）
                for (int i = 0; i < paragraphs.size(); i++) {
                    XWPFParagraph tocParagraph = paragraphs.get(i);
                    XWPFParagraph newParagraph = document.insertNewParagraph(firstParagraph.getCTP().newCursor());

                    // 复制段落内容
                    copyParagraphContent(tocParagraph, newParagraph);
                }

                // 删除原来创建的临时段落（从后往前删除，避免索引变化）
                for (int i = paragraphs.size() - 1; i >= 0; i--) {
                    XWPFParagraph p = paragraphs.get(i);
                    int index = document.getParagraphs().indexOf(p);
                    if (index != -1) {
                        document.removeBodyElement(index);
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("插入目录段落时出现异常: " + e.getMessage());
            // 如果插入失败，至少保留目录段落在文档末尾
            System.out.println("目录段落已添加到文档末尾");
        }
    }

    /**
     * 复制段落内容
     */
    private static void copyParagraphContent(XWPFParagraph source, XWPFParagraph target) {
        try {
            // 复制段落属性
            target.setAlignment(source.getAlignment());
            target.setIndentationLeft(source.getIndentationLeft());
            target.setIndentationRight(source.getIndentationRight());

            // 复制制表位设置
            if (source.getCTP().isSetPPr() && source.getCTP().getPPr().isSetTabs()) {
                CTPPr targetPPr = target.getCTP().isSetPPr() ? target.getCTP().getPPr() : target.getCTP().addNewPPr();
                targetPPr.setTabs(source.getCTP().getPPr().getTabs());
            }

            // 复制段落中的所有内容（包括超链接）
            target.getCTP().set(source.getCTP().copy());

        } catch (Exception e) {
            // 如果复制失败，使用简单的文本复制
            System.out.println("复制段落内容失败，使用简单文本复制: " + e.getMessage());
            XWPFRun targetRun = target.createRun();
            targetRun.setText(source.getText());
            targetRun.setFontFamily("宋体");
            targetRun.setFontSize(12);
        }
    }

    /**
     * 6. 保存文档
     */
    private static void saveDocument(XWPFDocument document, String docPath) throws Exception {
        try (FileOutputStream fos = new FileOutputStream(docPath)) {
            document.write(fos);
        }
        document.close();
        System.out.println("文档已保存: " + docPath);
    }
}