package top.resty;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.math.BigInteger;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

public class Main {

    // 标题信息类
    static class HeadingInfo {
        private String text;
        private String styleId;
        private int level;
        private XWPFParagraph paragraph;
        private String bookmarkName;
        private int pageNumber;

        public HeadingInfo(String text, String styleId, int level, XWPFParagraph paragraph) {
            this.text = text;
            this.styleId = styleId;
            this.level = level;
            this.paragraph = paragraph;
            this.bookmarkName = generateSafeBookmarkName(text);
            this.pageNumber = 1; // 默认页码
        }

        /**
         * 生成安全的书签名称
         */
        private static String generateSafeBookmarkName(String text) {
            // 移除特殊字符，只保留字母、数字和下划线
            String safeName = text.replaceAll("[^\\w\\u4e00-\\u9fa5]", "_");
            // 确保以字母开头
            if (!safeName.matches("^[a-zA-Z\\u4e00-\\u9fa5].*")) {
                safeName = "heading_" + safeName;
            }
            // 限制长度并添加唯一标识
            if (safeName.length() > 30) {
                safeName = safeName.substring(0, 30);
            }
            safeName += "_" + System.currentTimeMillis() % 10000;
            return safeName;
        }

        // Getters and Setters
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public String getStyleId() { return styleId; }
        public void setStyleId(String styleId) { this.styleId = styleId; }
        public int getLevel() { return level; }
        public void setLevel(int level) { this.level = level; }
        public XWPFParagraph getParagraph() { return paragraph; }
        public void setParagraph(XWPFParagraph paragraph) { this.paragraph = paragraph; }
        public String getBookmarkName() { return bookmarkName; }
        public void setBookmarkName(String bookmarkName) { this.bookmarkName = bookmarkName; }
        public int getPageNumber() { return pageNumber; }
        public void setPageNumber(int pageNumber) { this.pageNumber = pageNumber; }
    }

    public static void main(String[] args) {
        try {
            String originalDocPath = "测试1.docx";  // 修改为实际的源文件名
            String newDocPath = "generated_document_with_toc.docx";

            // 1. 加载原文档
            XWPFDocument originalDocument = loadDocument(originalDocPath);
            System.out.println("成功加载原文档: " + originalDocPath);

            // 2. 先分析文档结构
            analyzeDocumentStructure(originalDocument);

            // 3. 遍历出原文档中所有的标题并存进集合
            List<HeadingInfo> headings = extractHeadings(originalDocument);

            if (headings.isEmpty()) {
                System.out.println("原文档中没有找到标题");
                originalDocument.close();
                return;
            }

            // 3. 创建新文档，复制原文档内容
            XWPFDocument newDocument = createDocumentCopy(originalDocument);
            originalDocument.close(); // 关闭原文档，不再修改

            // 4. 在新文档中为标题添加书签
            List<HeadingInfo> newHeadings = extractHeadings(newDocument);
            addBookmarksToHeadings(newHeadings);

            // 5. 先用临时页码插入目录
            calculateTemporaryPageNumbers(newHeadings);

            // 6. 在新文档最初的位置插入目录
            insertTableOfContents(newDocument, newHeadings);

            // 7. 计算目录实际占用的页数，重新计算页码
            int tocPages = calculateTocPages(newDocument, newHeadings.size());
            recalculatePageNumbers(newHeadings, tocPages);

            // 8. 更新目录中的页码
            updateTocPageNumbers(newDocument, newHeadings);

            // 9. 保存新文档
            saveDocument(newDocument, newDocPath);

            System.out.println("新文档生成成功: " + newDocPath);
            System.out.println("原文档保持不变: " + originalDocPath);
            System.out.println("找到 " + newHeadings.size() + " 个标题");
            System.out.println("目录占用 " + tocPages + " 页");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    /**
     * 1. 加载文档
     */
    private static XWPFDocument loadDocument(String docPath) throws Exception {
        try (FileInputStream fis = new FileInputStream(docPath)) {
            return new XWPFDocument(fis);
        }
    }

    /**
     * 创建文档副本
     */
    private static XWPFDocument createDocumentCopy(XWPFDocument originalDocument) throws Exception {
        XWPFDocument newDocument = new XWPFDocument();

        // 复制所有段落
        for (XWPFParagraph originalParagraph : originalDocument.getParagraphs()) {
            XWPFParagraph newParagraph = newDocument.createParagraph();
            copyParagraph(originalParagraph, newParagraph);
        }

        System.out.println("文档副本创建完成，共复制 " + originalDocument.getParagraphs().size() + " 个段落");
        return newDocument;
    }

    /**
     * 复制段落内容
     */
    private static void copyParagraph(XWPFParagraph source, XWPFParagraph target) {
        // 复制段落属性
        target.setAlignment(source.getAlignment());
        target.setIndentationLeft(source.getIndentationLeft());
        target.setIndentationRight(source.getIndentationRight());
        target.setSpacingAfter(source.getSpacingAfter());
        target.setSpacingBefore(source.getSpacingBefore());

        // 复制样式
        if (source.getStyleID() != null) {
            target.setStyle(source.getStyleID());
        }

        // 复制所有运行
        for (XWPFRun sourceRun : source.getRuns()) {
            XWPFRun targetRun = target.createRun();
            copyRun(sourceRun, targetRun);
        }
    }

    /**
     * 复制运行内容
     */
    private static void copyRun(XWPFRun source, XWPFRun target) {
        // 复制文本
        String text = source.getText(0);
        if (text != null) {
            target.setText(text);
        }

        // 复制格式
        target.setBold(source.isBold());
        target.setItalic(source.isItalic());
        target.setUnderline(source.getUnderline());

        if (source.getFontFamily() != null) {
            target.setFontFamily(source.getFontFamily());
        }

        if (source.getFontSize() != -1) {
            target.setFontSize(source.getFontSize());
        }

        if (source.getColor() != null) {
            target.setColor(source.getColor());
        }

        // 复制分页符
        if (source.getCTR().getBrList().size() > 0) {
            for (int i = 0; i < source.getCTR().getBrList().size(); i++) {
                target.addBreak(BreakType.PAGE);
            }
        }
    }

    /**
     * 分析文档结构 - 全面了解文档的层级关系
     */
    private static void analyzeDocumentStructure(XWPFDocument document) {
        System.out.println("\n=== 开始分析文档结构 ===");

        List<XWPFParagraph> paragraphs = document.getParagraphs();
        System.out.println("文档总段落数: " + paragraphs.size());

        // 统计不同格式的段落
        int totalParagraphs = 0;
        int centeredParagraphs = 0;
        int numberedParagraphs = 0;
        int chineseNumberedParagraphs = 0;
        int bracketNumberedParagraphs = 0;

        System.out.println("\n=== 段落格式分析 ===");

        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();

            if (text == null || text.trim().isEmpty()) {
                continue;
            }

            text = text.trim();
            totalParagraphs++;

            // 获取对齐方式
            ParagraphAlignment alignment = paragraph.getAlignment();
            String alignmentStr = getAlignmentString(alignment);

            // 分析不同的编号格式
            boolean isNumbered = text.matches("^\\d+\\.[^\\d].*");
            boolean isSubNumbered = text.matches("^\\d+\\.\\d+.*");
            boolean isChineseNumbered = text.matches("^[一二三四五六七八九十百千万]+[、．.。].*");
            boolean isBracketNumbered = text.matches("^[（(【][一二三四五六七八九十百千万\\d]+[）)】].*");
            boolean isCircleNumbered = text.matches("^[①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳].*");

            // 统计
            if (alignment == ParagraphAlignment.CENTER) {
                centeredParagraphs++;
            }
            if (isNumbered || isSubNumbered) {
                numberedParagraphs++;
            }
            if (isChineseNumbered) {
                chineseNumberedParagraphs++;
            }
            if (isBracketNumbered) {
                bracketNumberedParagraphs++;
            }

            // 显示前100个有格式的段落
            if ((isNumbered || isSubNumbered || isChineseNumbered || isBracketNumbered ||
                 alignment == ParagraphAlignment.CENTER || isCircleNumbered) &&
                totalParagraphs <= 100) {

                String format = "";
                if (isNumbered) format += "[数字标题] ";
                if (isSubNumbered) format += "[子数字标题] ";
                if (isChineseNumbered) format += "[中文数字] ";
                if (isBracketNumbered) format += "[括号数字] ";
                if (isCircleNumbered) format += "[圆圈数字] ";
                if (alignment == ParagraphAlignment.CENTER) format += "[居中] ";

                System.out.println(String.format("第%d段: %s | %s | %s",
                    i + 1, format, alignmentStr,
                    text.length() > 50 ? text.substring(0, 50) + "..." : text));
            }
        }

        System.out.println("\n=== 统计结果 ===");
        System.out.println("总有效段落数: " + totalParagraphs);
        System.out.println("居中段落数: " + centeredParagraphs);
        System.out.println("数字编号段落数: " + numberedParagraphs);
        System.out.println("中文数字段落数: " + chineseNumberedParagraphs);
        System.out.println("括号编号段落数: " + bracketNumberedParagraphs);

        System.out.println("\n=== 文档结构分析完成 ===\n");
    }

    /**
     * 3. 遍历文档中所有的标题并存进集合
     */
    private static List<HeadingInfo> extractHeadings(XWPFDocument document) {
        List<HeadingInfo> headings = new ArrayList<>();

        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();

            if (text != null && !text.trim().isEmpty()) {
                text = text.trim();

                // 基于文本内容和段落格式识别标题级别
                int level = analyzeTextHeadingLevel(text, paragraph);
                if (level > 0) {
                    String styleId = paragraph.getStyleID();
                    if (styleId == null) styleId = "";

                    HeadingInfo heading = new HeadingInfo(text, styleId, level, paragraph);
                    headings.add(heading);

                    // 显示对齐方式信息
                    String alignment = getAlignmentString(paragraph.getAlignment());
                    System.out.println("找到标题: " + text + " (级别: " + level + ", 对齐: " + alignment + ")");
                }
            }
        }

        return headings;
    }

    /**
     * 分析文本内容的标题级别 - 只显示前2级标题，且必须是加粗的
     */
    private static int analyzeTextHeadingLevel(String text, XWPFParagraph paragraph) {
        // 过滤掉目录标题
        if (text.equals("目录")) {
            return 0;
        }

        // 检查段落是否包含加粗文本
        if (!isBoldText(paragraph)) {
            return 0; // 不是加粗文本，不是标题
        }

        // 检查段落对齐方式
        ParagraphAlignment alignment = paragraph.getAlignment();
        boolean isCentered = (alignment == ParagraphAlignment.CENTER);

        // 1级标题: 居中对齐 + 数字.xxx格式 + 加粗
        if (isCentered) {
            if (text.matches("^\\d+\\.[^\\d].*") ||  // 1.xxx格式
                text.matches("^第[一二三四五六七八九十百千万\\d]+[章节部分篇].*")) {  // 第X章格式
                return 1;
            }
        }

        // 2级标题: 左对齐 + 数字.数字.xxx格式 + 加粗
        if (!isCentered && text.matches("^\\d+\\.\\d+.*")) {
            return 2;
        }

        // 暂时注释掉3级、4级和5级标题，只显示前2级
        /*
        // 3级标题: 左对齐 + 中文数字格式 + 加粗
        if (!isCentered && text.matches("^[一二三四五六七八九十百千万]+[、．.。].*")) {
            return 3;
        }

        // 4级标题: 左对齐 + 圆圈数字格式 + 加粗
        if (!isCentered && text.matches("^[①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳].*")) {
            return 4;
        }

        // 5级标题: 左对齐 + 括号格式 + 加粗
        if (!isCentered && text.matches("^[（(【][一二三四五六七八九十百千万\\d]+[）)】].*")) {
            return 5;
        }
        */

        return 0; // 不是标题格式
    }

    /**
     * 获取对齐方式的字符串表示
     */
    private static String getAlignmentString(ParagraphAlignment alignment) {
        if (alignment == null) {
            return "默认";
        }
        switch (alignment) {
            case LEFT:
                return "左对齐";
            case CENTER:
                return "居中";
            case RIGHT:
                return "右对齐";
            case BOTH:
                return "两端对齐";
            default:
                return "未知";
        }
    }

    /**
     * 3. 为标题添加书签
     */
    private static void addBookmarksToHeadings(List<HeadingInfo> headings) {
        long bookmarkId = 1; // 从1开始，避免ID冲突

        for (HeadingInfo heading : headings) {
            XWPFParagraph paragraph = heading.getParagraph();
            String bookmarkName = heading.getBookmarkName();

            try {
                // 清除段落中可能存在的旧书签
                clearExistingBookmarks(paragraph);

                // 添加书签开始标记（在段落开头）
                CTBookmark bookmarkStart = paragraph.getCTP().insertNewBookmarkStart(0);
                bookmarkStart.setName(bookmarkName);
                bookmarkStart.setId(BigInteger.valueOf(bookmarkId));

                // 添加书签结束标记（在段落末尾）
                CTMarkupRange bookmarkEnd = paragraph.getCTP().addNewBookmarkEnd();
                bookmarkEnd.setId(BigInteger.valueOf(bookmarkId));

                bookmarkId++;

                System.out.println("为标题添加书签: " + heading.getText() + " -> " + bookmarkName);
            } catch (Exception e) {
                System.out.println("添加书签失败: " + heading.getText() + " - " + e.getMessage());
            }
        }
    }

    /**
     * 清除段落中现有的书签
     */
    private static void clearExistingBookmarks(XWPFParagraph paragraph) {
        try {
            // 移除现有的书签开始标记
            for (int i = paragraph.getCTP().getBookmarkStartList().size() - 1; i >= 0; i--) {
                paragraph.getCTP().removeBookmarkStart(i);
            }
            // 移除现有的书签结束标记
            for (int i = paragraph.getCTP().getBookmarkEndList().size() - 1; i >= 0; i--) {
                paragraph.getCTP().removeBookmarkEnd(i);
            }
        } catch (Exception e) {
            // 忽略清除错误
        }
    }

    /**
     * 4. 计算临时页码（用于初始目录生成）
     */
    private static void calculateTemporaryPageNumbers(List<HeadingInfo> headings) {
        int currentPage = 1; // 临时从第1页开始

        for (HeadingInfo heading : headings) {
            if (heading.getLevel() == 1) {
                // 一级标题，新的一页
                currentPage++;
                heading.setPageNumber(currentPage);
            } else {
                // 二级标题，在当前页
                heading.setPageNumber(currentPage);
            }
        }
        System.out.println("临时页码计算完成");
    }

    /**
     * 计算目录占用的页数（更精确的计算）
     */
    private static int calculateTocPages(XWPFDocument document, int headingCount) {
        // 更精确的估算：
        // 1. 目录标题：1行
        // 2. 空行：1行
        // 3. 目录条目：headingCount行
        // 4. 分页符：1行
        // 假设每页可以容纳约45行（考虑页边距）
        int linesPerPage = 45;
        int totalLines = 1 + 1 + headingCount + 1; // 标题 + 空行 + 条目 + 分页符
        int tocPages = (int) Math.ceil((double) totalLines / linesPerPage);

        // 至少1页
        if (tocPages < 1) tocPages = 1;

        System.out.println("估算目录页数: " + tocPages + " 页 (共 " + headingCount + " 个条目, " + totalLines + " 行)");
        return tocPages;
    }

    /**
     * 根据目录页数重新计算页码
     */
    private static void recalculatePageNumbers(List<HeadingInfo> headings, int tocPages) {
        int currentPage = tocPages; // 内容从目录后开始

        for (HeadingInfo heading : headings) {
            if (heading.getLevel() == 1) {
                // 一级标题，新的一页
                currentPage++;
                heading.setPageNumber(currentPage);
            } else {
                // 二级标题，在当前页
                heading.setPageNumber(currentPage);
            }

            System.out.println("重新计算页码: " + heading.getText() + " -> 第" + heading.getPageNumber() + "页");
        }
    }

    /**
     * 5. 在文档最初的位置插入目录
     */
    private static void insertTableOfContents(XWPFDocument document, List<HeadingInfo> headings) {
        if (headings.isEmpty()) {
            System.out.println("没有找到标题，无法生成目录");
            return;
        }

        // 获取第一个段落的位置
        XWPFParagraph firstParagraph = document.getParagraphs().get(0);

        // 在第一个段落前插入目录标题
        XWPFParagraph tocTitleParagraph = document.insertNewParagraph(firstParagraph.getCTP().newCursor());
        tocTitleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun tocTitleRun = tocTitleParagraph.createRun();
        tocTitleRun.setText("目录");
        tocTitleRun.setBold(true);
        tocTitleRun.setFontSize(16);
        tocTitleRun.setFontFamily("宋体");

        // 添加空行
        XWPFParagraph emptyLine = document.insertNewParagraph(firstParagraph.getCTP().newCursor());

        // 为每个标题创建目录条目
        for (HeadingInfo heading : headings) {
            XWPFParagraph tocEntry = document.insertNewParagraph(firstParagraph.getCTP().newCursor());
            createTocEntryInParagraph(tocEntry, heading);
        }

        // 添加分页符
        XWPFParagraph pageBreak = document.insertNewParagraph(firstParagraph.getCTP().newCursor());
        XWPFRun pageBreakRun = pageBreak.createRun();
        pageBreakRun.addBreak(BreakType.PAGE);

        System.out.println("目录插入完成，共 " + headings.size() + " 个条目");
    }

    /**
     * 更新目录中的页码
     */
    private static void updateTocPageNumbers(XWPFDocument document, List<HeadingInfo> headings) {
        System.out.println("开始更新目录页码...");

        // 找到所有目录段落并更新页码
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int headingIndex = 0;

        for (XWPFParagraph paragraph : paragraphs) {
            // 检查段落是否包含超链接（更简单的检测方法）
            if (containsHyperlink(paragraph) && headingIndex < headings.size()) {
                HeadingInfo heading = headings.get(headingIndex);
                updateParagraphPageNumber(paragraph, heading.getPageNumber());
                System.out.println("更新页码: " + heading.getText() + " -> 第" + heading.getPageNumber() + "页");
                headingIndex++;
            }
        }

        System.out.println("目录页码更新完成");
    }

    /**
     * 检查段落是否包含超链接
     */
    private static boolean containsHyperlink(XWPFParagraph paragraph) {
        // 检查段落是否包含超链接运行
        for (XWPFRun run : paragraph.getRuns()) {
            if (run instanceof XWPFHyperlinkRun) {
                return true;
            }
        }

        // 检查段落文本是否包含标题文本（作为备选检测方法）
        String paragraphText = paragraph.getText();
        if (paragraphText != null && (paragraphText.contains("章") || paragraphText.contains("."))) {
            return true;
        }

        return false;
    }

    /**
     * 更新段落中的页码
     */
    private static void updateParagraphPageNumber(XWPFParagraph paragraph, int newPageNumber) {
        // 找到段落中最后一个文本运行（通常是页码）
        List<XWPFRun> runs = paragraph.getRuns();
        if (!runs.isEmpty()) {
            // 从后往前查找数字（页码）
            for (int i = runs.size() - 1; i >= 0; i--) {
                XWPFRun run = runs.get(i);
                String text = run.getText(0);
                if (text != null && text.matches("\\d+")) {
                    run.setText(String.valueOf(newPageNumber), 0);
                    System.out.println("  页码已更新: " + text + " -> " + newPageNumber);
                    return;
                }
            }

            // 如果没找到数字，在最后添加页码
            XWPFRun lastRun = runs.get(runs.size() - 1);
            String currentText = lastRun.getText(0);
            if (currentText != null && !currentText.matches("\\d+")) {
                lastRun.setText(String.valueOf(newPageNumber), 0);
                System.out.println("  添加页码: " + newPageNumber);
            }
        }
    }

    /**
     * 在指定段落中创建目录条目
     */
    private static void createTocEntryInParagraph(XWPFParagraph tocParagraph, HeadingInfo heading) {
        // 设置缩进（二级标题及以下缩进）
        if (heading.getLevel() > 1) {
            tocParagraph.setIndentationLeft(400 * (heading.getLevel() - 1));
        }

        // 设置制表位（右对齐，点状引导线）
        CTPPr pPr = tocParagraph.getCTP().isSetPPr() ? tocParagraph.getCTP().getPPr() : tocParagraph.getCTP().addNewPPr();
        CTTabs tabs = pPr.isSetTabs() ? pPr.getTabs() : pPr.addNewTabs();

        // 清除现有制表位
        if (tabs.getTabList().size() > 0) {
            tabs.setTabArray(null);
        }

        // 设置制表位：右对齐，点状引导线（虚线）
        CTTabStop tabStop = tabs.addNewTab();
        tabStop.setVal(STTabJc.RIGHT);
        tabStop.setLeader(STTabTlc.DOT); // 点状引导线（虚线）
        tabStop.setPos(BigInteger.valueOf(8500)); // 制表位位置

        // 创建超链接到书签
        XWPFHyperlinkRun hyperlinkRun = tocParagraph.createHyperlinkRun("#" + heading.getBookmarkName());
        hyperlinkRun.setText(heading.getText());
        hyperlinkRun.setFontFamily("宋体");
        hyperlinkRun.setFontSize(12);
        hyperlinkRun.setColor("0000FF"); // 蓝色
        hyperlinkRun.setUnderline(UnderlinePatterns.SINGLE); // 下划线

        System.out.println("创建超链接: " + heading.getText() + " -> " + heading.getBookmarkName());

        // 添加制表符（这会触发虚线填充）
        XWPFRun tabRun = tocParagraph.createRun();
        tabRun.addTab();

        // 添加页码
        XWPFRun pageRun = tocParagraph.createRun();
        pageRun.setText(String.valueOf(heading.getPageNumber()));
        pageRun.setFontFamily("宋体");
        pageRun.setFontSize(12);

        System.out.println("创建目录条目: " + heading.getText() + " -> 第" + heading.getPageNumber() + "页");
    }



    /**
     * 6. 保存文档
     */
    private static void saveDocument(XWPFDocument document, String docPath) throws Exception {
        try (FileOutputStream fos = new FileOutputStream(docPath)) {
            document.write(fos);
        }
        document.close();
        System.out.println("文档已保存: " + docPath);
    }
}