package top.resty;

import java.io.FileOutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.policy.AbstractRenderPolicy;
import com.deepoove.poi.policy.RenderPolicy;
import com.deepoove.poi.template.ElementTemplate;
import com.deepoove.poi.template.run.RunTemplate;
import com.deepoove.poi.config.Configure;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import java.math.BigInteger;

public class Main {

    // 章节数据模型
    static class ChapterData {
        private String title;
        private String content;
        private List<SectionData> sections;

        public ChapterData(String title, String content) {
            this.title = title;
            this.content = content;
            this.sections = new ArrayList<>();
        }

        // Getters and Setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public List<SectionData> getSections() { return sections; }
        public void setSections(List<SectionData> sections) { this.sections = sections; }
    }

    // 小节数据模型
    static class SectionData {
        private String title;
        private String content;

        public SectionData(String title, String content) {
            this.title = title;
            this.content = content;
        }

        // Getters and Setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
    }

    // 目录条目数据模型
    static class TocEntry {
        private String title;
        private int page;
        private int level; // 1为一级标题，2为二级标题

        public TocEntry(String title, int page, int level) {
            this.title = title;
            this.page = page;
            this.level = level;
        }

        // Getters and Setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public int getPage() { return page; }
        public void setPage(int page) { this.page = page; }
        public int getLevel() { return level; }
        public void setLevel(int level) { this.level = level; }
    }

    // 自定义目录渲染策略
    static class TocRenderPolicy implements RenderPolicy {
        @Override
        public void render(ElementTemplate eleTemplate, Object data, XWPFTemplate template) {
            XWPFRun run = ((RunTemplate) eleTemplate).getRun();
            XWPFParagraph paragraph = (XWPFParagraph) run.getParent();
            XWPFDocument document = paragraph.getDocument();

            @SuppressWarnings("unchecked")
            List<TocEntry> tocEntries = (List<TocEntry>) data;

            // 清除原有内容
            run.setText("", 0);

            // 创建目录条目
            for (TocEntry entry : tocEntries) {
                XWPFParagraph tocPara = document.insertNewParagraph(paragraph.getCTP().newCursor());

                // 设置缩进（二级标题缩进）
                if (entry.getLevel() == 2) {
                    tocPara.setIndentationLeft(400);
                }

                // 设置制表位
                CTPPr pPr = tocPara.getCTP().isSetPPr() ? tocPara.getCTP().getPPr() : tocPara.getCTP().addNewPPr();
                CTTabs tabs = pPr.isSetTabs() ? pPr.getTabs() : pPr.addNewTabs();
                CTTabStop tabStop = tabs.addNewTab();
                tabStop.setVal(STTabJc.RIGHT);
                tabStop.setLeader(STTabTlc.DOT);
                tabStop.setPos(BigInteger.valueOf(9000));

                // 添加标题文本
                XWPFRun titleRun = tocPara.createRun();
                titleRun.setText(entry.getTitle());
                titleRun.setFontFamily("宋体");
                titleRun.setFontSize(12);

                // 添加制表符
                XWPFRun tabRun = tocPara.createRun();
                tabRun.addTab();

                // 添加页码
                XWPFRun pageRun = tocPara.createRun();
                pageRun.setText(String.valueOf(entry.getPage()));
                pageRun.setFontFamily("宋体");
                pageRun.setFontSize(12);
            }
        }
    }

    public static void main(String[] args) {
        try {
            // 创建模板数据
            Map<String, Object> data = createTemplateData();

            // 手动创建目录数据
            List<TocEntry> tocEntries = createTocEntries();
            data.put("toc", tocEntries);

            // 配置自定义目录渲染策略
            Configure config = Configure.builder()
                .bind("toc", new TocRenderPolicy())
                .build();

            // 创建简单的文档结构
            DocumentRenderData document = createSimpleDocument();

            // 使用无模板方式创建文档
            XWPFTemplate template = XWPFTemplate.create(document, config);
            template.render(data);

            // 保存文档
            template.writeAndClose(new FileOutputStream("generated_document.docx"));

            System.out.println("Word文档生成成功: generated_document.docx");
            System.out.println("目录已生成，页码基于估算值。");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建模板数据
     */
    private static Map<String, Object> createTemplateData() {
        Map<String, Object> data = new HashMap<>();

        // 文档标题
        data.put("title", "文档标题");

        // 创建章节数据
        List<ChapterData> chapters = new ArrayList<>();

        for (int i = 1; i <= 20; i++) {
            ChapterData chapter = new ChapterData(
                "第" + i + "章 一级标题" + i,
                "这是第" + i + "章的内容介绍。本章将详细介绍相关主题的各个方面。"
            );

            // 为每章添加5个小节
            for (int j = 1; j <= 5; j++) {
                SectionData section = new SectionData(
                    i + "." + j + " 二级标题" + i + "." + j,
                    "这是第" + i + "章第" + j + "节的具体内容。在这里可以添加详细的说明和描述，包括相关的技术细节、实例分析和应用场景等。"
                );
                chapter.getSections().add(section);
            }

            chapters.add(chapter);
        }

        data.put("chapters", chapters);
        return data;
    }

    /**
     * 创建目录条目
     */
    private static List<TocEntry> createTocEntries() {
        List<TocEntry> tocEntries = new ArrayList<>();
        int currentPage = 2; // 从第2页开始（第1页是标题和目录）

        for (int i = 1; i <= 20; i++) {
            // 一级标题
            tocEntries.add(new TocEntry("第" + i + "章 一级标题" + i, currentPage, 1));

            // 二级标题（假设都在同一页）
            for (int j = 1; j <= 5; j++) {
                tocEntries.add(new TocEntry(i + "." + j + " 二级标题" + i + "." + j, currentPage, 2));
            }

            currentPage++; // 每章占一页
        }

        return tocEntries;
    }

    /**
     * 创建简单的文档结构
     */
    private static DocumentRenderData createSimpleDocument() {
        return Documents.of()
            // 文档标题
            .addParagraph(Paragraphs.of()
                .addText(Texts.of("{{title}}").bold().fontSize(20).fontFamily("宋体").create())
                .center()
                .create())

            // 空行
            .addParagraph(Paragraphs.of().create())

            // 目录标题
            .addParagraph(Paragraphs.of()
                .addText(Texts.of("目录").bold().fontSize(16).fontFamily("宋体").create())
                .center()
                .create())

            // 目录内容（使用自定义渲染策略）
            .addParagraph(Paragraphs.of("{{toc}}").create())

            // 分页符
            .addParagraph(Paragraphs.of("").create())

            // 章节循环
            .addParagraph(Paragraphs.of("{{?chapters}}").create())

            // 一级标题
            .addParagraph(Paragraphs.of()
                .addText(Texts.of("{{title}}").bold().fontSize(16).fontFamily("宋体").create())
                .create())

            // 章节内容
            .addParagraph(Paragraphs.of()
                .addText(Texts.of("{{content}}").fontFamily("宋体").fontSize(12).create())
                .create())

            // 空行
            .addParagraph(Paragraphs.of().create())

            // 二级标题循环
            .addParagraph(Paragraphs.of("{{?sections}}").create())

            // 二级标题
            .addParagraph(Paragraphs.of()
                .addText(Texts.of("{{title}}").bold().fontSize(14).fontFamily("宋体").create())
                .create())

            // 二级标题内容
            .addParagraph(Paragraphs.of()
                .addText(Texts.of("{{content}}").fontFamily("宋体").fontSize(12).create())
                .create())

            // 空行
            .addParagraph(Paragraphs.of().create())

            // 结束二级标题循环
            .addParagraph(Paragraphs.of("{{/sections}}").create())

            // 分页符（除了最后一章）
            .addParagraph(Paragraphs.of("").create())

            // 结束章节循环
            .addParagraph(Paragraphs.of("{{/chapters}}").create())

            .create();
    }

}