package top.resty;

import java.io.FileOutputStream;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSimpleField;

public class Main {
  public static void main(String[] args) {
    try (XWPFDocument doc = new XWPFDocument()) {
      // 创建文档标题
      XWPFParagraph titleParagraph = doc.createParagraph();
      titleParagraph.setAlignment(ParagraphAlignment.CENTER);
      XWPFRun titleRun = titleParagraph.createRun();
      titleRun.setText("文档标题");
      titleRun.setBold(true);
      titleRun.setFontSize(20);
      titleRun.setFontFamily("宋体");
      
      // 添加空行
      doc.createParagraph();
      
      // 创建目录标题
      XWPFParagraph tocTitleParagraph = doc.createParagraph();
      tocTitleParagraph.setAlignment(ParagraphAlignment.CENTER);
      XWPFRun tocTitleRun = tocTitleParagraph.createRun();
      tocTitleRun.setText("目录");
      tocTitleRun.setBold(true);
      tocTitleRun.setFontSize(16);
      tocTitleRun.setFontFamily("宋体");
      
      // 创建目录字段
      XWPFParagraph tocParagraph = doc.createParagraph();
      CTP ctP = tocParagraph.getCTP();
      CTR ctR = ctP.addNewR();
      // 创建字段结构（修正字段指令格式）
      CTSimpleField tocField = ctP.addNewFldSimple();
      tocField.setInstr(" TOC \\o \"1-3\" \\h \\z \\u ");  // 使用标准目录指令
      tocField.setDirty(true);
      
      // 添加分页符
      XWPFParagraph pageBreakParagraph = doc.createParagraph();
      XWPFRun pageBreakRun = pageBreakParagraph.createRun();
      pageBreakRun.addBreak(BreakType.PAGE);
      
      // 创建20个一级标题，每个一级标题下有5个二级标题
      for (int i = 1; i <= 20; i++) {
        // 创建一级标题
        XWPFParagraph h1Paragraph = doc.createParagraph();
        h1Paragraph.setStyle("Heading1");
        XWPFRun h1Run = h1Paragraph.createRun();
        h1Run.setText("第" + i + "章 一级标题" + i);
        h1Run.setBold(true);
        h1Run.setFontSize(16);
        h1Run.setFontFamily("宋体");
        
        // 在一级标题下添加一些内容
        XWPFParagraph contentParagraph = doc.createParagraph();
        XWPFRun contentRun = contentParagraph.createRun();
        contentRun.setText("这是第" + i + "章的内容介绍。");
        contentRun.setFontFamily("宋体");
        
        // 创建5个二级标题
        for (int j = 1; j <= 5; j++) {
          XWPFParagraph h2Paragraph = doc.createParagraph();
          h2Paragraph.setStyle("Heading2");
          XWPFRun h2Run = h2Paragraph.createRun();
          h2Run.setText(i + "." + j + " 二级标题" + i + "." + j);
          h2Run.setBold(true);
          h2Run.setFontSize(14);
          h2Run.setFontFamily("宋体");
          
          // 在二级标题下添加一些内容
          XWPFParagraph subContentParagraph = doc.createParagraph();
          XWPFRun subContentRun = subContentParagraph.createRun();
          subContentRun.setText("这是第" + i + "章第" + j + "节的具体内容。在这里可以添加详细的说明和描述。");
          subContentRun.setFontFamily("宋体");
          
          // 添加空行
          doc.createParagraph();
        }
        
        // 在每章之间添加分页符（除了最后一章）
        if (i < 20) {
          XWPFParagraph chapterBreakParagraph = doc.createParagraph();
          XWPFRun chapterBreakRun = chapterBreakParagraph.createRun();
          chapterBreakRun.addBreak(BreakType.PAGE);
        }
      }

      XWPFStyle style = doc.createStyles().getStyle("Heading1");

      // 保存文档
      try (FileOutputStream out = new FileOutputStream("generated_document.docx")) {
        doc.write(out);
        System.out.println("文档已成功生成：generated_document.docx");
      }
    } catch (Exception e) {
      e.printStackTrace();
    }
  }
}