package top.resty;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigInteger;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import com.deepoove.poi.data.*;

import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import org.apache.xmlbeans.XmlCursor;

public class Main {

    // Helper class to store heading information
    static class HeadingInfo {
        String text;
        String bookmarkName;
        int level;

        public HeadingInfo(String text, String bookmarkName, int level) {
            this.text = text;
            this.bookmarkName = bookmarkName;
            this.level = level;
        }
    }

    public static void main(String[] args) {
        try {
            String docPath = "generated_document.docx";

            // 1. Create the base document with title and content (including bookmarks)
            XWPFDocument baseDocument = createBaseDocumentWithContent();
            try (FileOutputStream out = new FileOutputStream(docPath)) {
                baseDocument.write(out);
            }
            baseDocument.close();

            // 2. Reopen the document and add the dynamic Table of Contents
            XWPFDocument docToModify = new XWPFDocument(new FileInputStream(docPath));
            addDynamicTableOfContents(docToModify);

            // 3. Save the modified document
            try (FileOutputStream out = new FileOutputStream(docPath)) { // Overwrite existing file
                docToModify.write(out);
            }
            docToModify.close();

            System.out.println("Word文档生成成功 (含动态目录): " + docPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static XWPFDocument createBaseDocumentWithContent() throws IOException {
        XWPFDocument document = new XWPFDocument();
        createDocumentTitle(document); // Add title first
        createContent(document);       // Add main content with headings and bookmarks
        return document;
    }

    private static void createDocumentTitle(XWPFDocument document) {
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText("文档标题");
        titleRun.setBold(true);
        titleRun.setFontSize(20);
        titleRun.setFontFamily("宋体");
        
        // 添加空行
        document.createParagraph();
    }

    private static void addDynamicTableOfContents(XWPFDocument document) {
        List<HeadingInfo> headings = new ArrayList<>();
        for (XWPFParagraph p : document.getParagraphs()) {
            String styleId = p.getStyleID();
            String paragraphText = p.getText(); // Get full paragraph text
            if (paragraphText == null || paragraphText.trim().isEmpty()) {
                continue; // Skip empty paragraphs
            }

            if (styleId != null) {
                String bookmark = findBookmarkForParagraph(p);
                if (bookmark != null) {
                    if (styleId.equalsIgnoreCase("Heading1") || styleId.equalsIgnoreCase("heading1")) {
                        headings.add(new HeadingInfo(paragraphText, bookmark, 1));
                    } else if (styleId.equalsIgnoreCase("Heading2") || styleId.equalsIgnoreCase("heading2")) {
                        headings.add(new HeadingInfo(paragraphText, bookmark, 2));
                    }
                }
            }
        }

        if (headings.isEmpty()) {
            System.out.println("未找到标题，无法生成目录。");
            return;
        }

        // Create a list to hold new TOC paragraphs temporarily
        List<XWPFParagraph> tocTempParagraphs = new ArrayList<>();

        XWPFParagraph tocTitleP = document.createParagraph(); // Temporary, will be discarded after CTP copy
        tocTitleP.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun tocTitleR = tocTitleP.createRun();
        tocTitleR.setText("目录");
        tocTitleR.setBold(true);
        tocTitleR.setFontSize(16);
        tocTitleR.setFontFamily("宋体");
        tocTempParagraphs.add(tocTitleP);
        tocTempParagraphs.add(document.createParagraph()); // Blank line

        for (HeadingInfo heading : headings) {
            XWPFParagraph itemP = document.createParagraph(); // Temporary
            if (heading.level == 2) {
                itemP.setIndentationLeft(400); // Indent for level 2 headings
            }
            createTocEntry(itemP, heading.text, heading.bookmarkName);
            tocTempParagraphs.add(itemP);
        }

        // Determine insertion point: after the title (assuming title is 2 paragraphs)
        // This needs to be robust. If createDocumentTitle creates exactly 2 paragraphs (title + blank line),
        // then existing content starts at index 2. We insert TOC before that.
        int insertPosIndex = 2; // Default: after title and its blank line
        if (document.getParagraphs().size() < insertPosIndex) {
            insertPosIndex = document.getParagraphs().size(); // Insert at end if doc is too short
        }

        // Insert TOC paragraphs in reverse order to maintain sequence at insertPosIndex
        for (int i = tocTempParagraphs.size() - 1; i >= 0; i--) {
            XWPFParagraph pToCopyFrom = tocTempParagraphs.get(i);
            XmlCursor cursor = document.getParagraphs().get(insertPosIndex).getCTP().newCursor();
            XWPFParagraph newInsertedP = document.insertNewParagraph(cursor);
            newInsertedP.getCTP().set(pToCopyFrom.getCTP().copy());
        }
        
        // Clean up temporary paragraphs created at the end of the document
        for(int i=0; i<tocTempParagraphs.size(); ++i){
            document.removeBodyElement(document.getBodyElements().size() -1 );
        }

        // Add a page break after the TOC and before the main content
        // The new TOC content has been inserted at insertPosIndex.
        // The page break should go after all inserted TOC paragraphs.
        int pageBreakInsertIndex = insertPosIndex + tocTempParagraphs.size();
        if (pageBreakInsertIndex <= document.getParagraphs().size()) {
             XmlCursor pageBreakCursor = document.getParagraphs().get(pageBreakInsertIndex).getCTP().newCursor();
             XWPFParagraph pageBreakP = document.insertNewParagraph(pageBreakCursor);
             pageBreakP.setPageBreak(true);
        } else { // If inserting at the very end
            XWPFParagraph pageBreakP = document.createParagraph();
            pageBreakP.setPageBreak(true);
        }
    }

    private static String findBookmarkForParagraph(XWPFParagraph p) {
        if (p.getCTP() != null) {
            for (CTBookmark bookmarkStart : p.getCTP().getBookmarkStartList()) {
                return bookmarkStart.getName();
            }
        }
        return null;
    }

    private static void createTocEntry(XWPFParagraph paragraph, String text, String bookmarkName) {
        CTPPr pPr = paragraph.getCTP().isSetPPr() ? paragraph.getCTP().getPPr() : paragraph.getCTP().addNewPPr();
        CTTabs tabs = pPr.isSetTabs() ? pPr.getTabs() : pPr.addNewTabs();
        
        // Ensure only one tab stop for the TOC entry (right, dotted)
        // Clear existing tabs for this paragraph style if necessary, or ensure it's clean
        if (tabs.getTabList().size() > 0) { // Simple clear if any exist
            tabs.setTabArray(null);
        }

        CTTabStop tabStop = tabs.addNewTab();
        tabStop.setVal(STTabJc.RIGHT);
        tabStop.setLeader(STTabTlc.DOT);
        tabStop.setPos(BigInteger.valueOf(9000)); // Adjust as needed (approx 6.25 inches)

        XWPFHyperlinkRun hyperlinkRun = paragraph.createHyperlinkRun("#" + bookmarkName);
        hyperlinkRun.setText(text);
        hyperlinkRun.setFontFamily("宋体");
        hyperlinkRun.setFontSize(12);
        hyperlinkRun.setColor("0000FF");
        hyperlinkRun.setUnderline(UnderlinePatterns.SINGLE);

        XWPFRun tabRun = paragraph.createRun();
        tabRun.addTab();

        // Page number using PAGEREF field
        XWPFRun fieldRunStart = paragraph.createRun();
        fieldRunStart.getCTR().addNewFldChar().setFldCharType(STFldCharType.BEGIN);

        XWPFRun fieldInstr = paragraph.createRun();
        CTText instr = fieldInstr.getCTR().addNewInstrText();
        instr.setSpace(SpaceAttribute.Space.PRESERVE); // Preserve spaces in instruction
        instr.setStringValue(" PAGEREF " + bookmarkName + " \\h "); // \h for hyperlink

        XWPFRun fieldRunSeparate = paragraph.createRun(); // Optional: if you want to show a default value before update
        // fieldRunSeparate.getCTR().addNewFldChar().setFldCharType(STFldCharType.SEPARATE);
        // XWPFRun fieldDefaultValue = paragraph.createRun();
        // fieldDefaultValue.setText("*"); // Placeholder, Word updates this

        XWPFRun fieldRunEnd = paragraph.createRun();
        fieldRunEnd.getCTR().addNewFldChar().setFldCharType(STFldCharType.END);
    }
    
    // Keep addPageBreak if used elsewhere, or remove if only for old TOC
    private static void addPageBreak(XWPFDocument document) {
        XWPFParagraph pageBreakParagraph = document.createParagraph();
        XWPFRun pageBreakRun = pageBreakParagraph.createRun();
        pageBreakRun.addBreak(BreakType.PAGE);
    }

    private static void createContent(XWPFDocument document) {
        for (int i = 1; i <= 20; i++) {
            // 创建一级标题
            XWPFParagraph h1Paragraph = document.createParagraph();
            h1Paragraph.setStyle("Heading1");
            
            // 为一级标题添加书签
            addBookmark(h1Paragraph, "heading1_" + i);
            
            XWPFRun h1Run = h1Paragraph.createRun();
            h1Run.setText("第" + i + "章 一级标题" + i);
            h1Run.setBold(true);
            h1Run.setFontSize(16);
            h1Run.setFontFamily("宋体");
            
            // 在一级标题下添加内容
            XWPFParagraph contentParagraph = document.createParagraph();
            XWPFRun contentRun = contentParagraph.createRun();
            contentRun.setText("这是第" + i + "章的内容介绍。本章将详细介绍相关主题的各个方面。");
            contentRun.setFontFamily("宋体");
            contentRun.setFontSize(12);
            
            // 添加空行
            document.createParagraph();
            
            // 创建5个二级标题
            for (int j = 1; j <= 5; j++) {
                XWPFParagraph h2Paragraph = document.createParagraph();
                h2Paragraph.setStyle("Heading2");
                
                // 为二级标题添加书签
                addBookmark(h2Paragraph, "heading2_" + i + "_" + j);
                
                XWPFRun h2Run = h2Paragraph.createRun();
                h2Run.setText(i + "." + j + " 二级标题" + i + "." + j);
                h2Run.setBold(true);
                h2Run.setFontSize(14);
                h2Run.setFontFamily("宋体");
                
                // 在二级标题下添加内容
                XWPFParagraph subContentParagraph = document.createParagraph();
                XWPFRun subContentRun = subContentParagraph.createRun();
                subContentRun.setText("这是第" + i + "章第" + j + "节的具体内容。在这里可以添加详细的说明和描述，包括相关的技术细节、实例分析和应用场景等。");
                subContentRun.setFontFamily("宋体");
                subContentRun.setFontSize(12);
                
                // 添加空行
                document.createParagraph();
            }
            
            // 在每章之间添加分页符（除了最后一章）
            if (i < 20) {
                addPageBreak(document);
            }
        }
    }
    
    private static void addBookmark(XWPFParagraph paragraph, String bookmarkName) {
        CTBookmark bookmarkStart = paragraph.getCTP().addNewBookmarkStart();
        bookmarkStart.setName(bookmarkName);
        bookmarkStart.setId(BigInteger.valueOf(bookmarkName.hashCode()));
        
        CTMarkupRange bookmarkEnd = paragraph.getCTP().addNewBookmarkEnd();
        bookmarkEnd.setId(BigInteger.valueOf(bookmarkName.hashCode()));
    }
}